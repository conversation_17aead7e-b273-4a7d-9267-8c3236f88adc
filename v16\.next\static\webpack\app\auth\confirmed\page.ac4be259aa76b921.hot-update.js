"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/confirmed/page",{

/***/ "(app-pages-browser)/./src/app/auth/confirmed/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/auth/confirmed/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfirmedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiCheckCircle_FiLoader_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiCheckCircle,FiLoader!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ConfirmedPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Verificando tu confirmación de email...');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfirmedPage.useEffect\": ()=>{\n            const handleEmailConfirmation = {\n                \"ConfirmedPage.useEffect.handleEmailConfirmation\": async ()=>{\n                    try {\n                        // Escuchar cambios en el estado de autenticación\n                        const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                            \"ConfirmedPage.useEffect.handleEmailConfirmation\": async (event, session)=>{\n                                console.log('Auth event:', event, 'Session:', session);\n                                if ((event === 'SIGNED_IN' || event === 'USER_UPDATED' || event === 'INITIAL_SESSION') && session) {\n                                    setMessage('Email confirmado exitosamente. Verificando tu perfil...');\n                                    try {\n                                        // Obtener el perfil del usuario para determinar el siguiente paso\n                                        const { data: profile, error: profileError } = await supabase.from('user_profiles').select('*').eq('user_id', session.user.id).single();\n                                        if (profileError) {\n                                            console.error('Error obteniendo perfil:', profileError);\n                                            setStatus('error');\n                                            setMessage('Error al verificar tu perfil. Por favor, contacta con soporte.');\n                                            return;\n                                        }\n                                        if (!profile) {\n                                            setStatus('error');\n                                            setMessage('No se encontró tu perfil. Por favor, contacta con soporte.');\n                                            return;\n                                        }\n                                        // Determinar redirección basada en el estado del perfil\n                                        if (profile.payment_verified === false && profile.subscription_plan !== 'free') {\n                                            // Usuario de pago pendiente de verificación\n                                            setMessage('Redirigiendo a completar tu pago...');\n                                            setStatus('redirecting');\n                                            setTimeout({\n                                                \"ConfirmedPage.useEffect.handleEmailConfirmation\": ()=>{\n                                                    router.push('/payment?plan=' + profile.subscription_plan);\n                                                }\n                                            }[\"ConfirmedPage.useEffect.handleEmailConfirmation\"], 2000);\n                                        } else if (profile.payment_verified === true || profile.subscription_plan === 'free') {\n                                            // Usuario completamente activado\n                                            setMessage('¡Cuenta activada! Redirigiendo a tu dashboard...');\n                                            setStatus('success');\n                                            setTimeout({\n                                                \"ConfirmedPage.useEffect.handleEmailConfirmation\": ()=>{\n                                                    router.push('/app');\n                                                }\n                                            }[\"ConfirmedPage.useEffect.handleEmailConfirmation\"], 2000);\n                                        } else {\n                                            // Estado inesperado\n                                            setStatus('error');\n                                            setMessage('Estado de cuenta inesperado. Por favor, contacta con soporte.');\n                                        }\n                                    } catch (error) {\n                                        console.error('Error procesando confirmación:', error);\n                                        setStatus('error');\n                                        setMessage('Error al procesar la confirmación. Por favor, intenta de nuevo.');\n                                    }\n                                } else if (event === 'SIGNED_OUT') {\n                                    setStatus('error');\n                                    setMessage('Error en la confirmación. Por favor, intenta de nuevo.');\n                                }\n                            }\n                        }[\"ConfirmedPage.useEffect.handleEmailConfirmation\"]);\n                        // Limpiar el listener cuando el componente se desmonte\n                        return ({\n                            \"ConfirmedPage.useEffect.handleEmailConfirmation\": ()=>{\n                                subscription.unsubscribe();\n                            }\n                        })[\"ConfirmedPage.useEffect.handleEmailConfirmation\"];\n                    } catch (error) {\n                        console.error('Error en handleEmailConfirmation:', error);\n                        setStatus('error');\n                        setMessage('Error al procesar la confirmación. Por favor, intenta de nuevo.');\n                    }\n                }\n            }[\"ConfirmedPage.useEffect.handleEmailConfirmation\"];\n            handleEmailConfirmation();\n        }\n    }[\"ConfirmedPage.useEffect\"], [\n        supabase,\n        router\n    ]);\n    const getIcon = ()=>{\n        switch(status){\n            case 'loading':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheckCircle_FiLoader_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLoader, {\n                    className: \"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 16\n                }, this);\n            case 'success':\n            case 'redirecting':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheckCircle_FiLoader_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCheckCircle, {\n                    className: \"w-12 h-12 text-green-600 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheckCircle_FiLoader_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiAlertTriangle, {\n                    className: \"w-12 h-12 text-red-600 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiCheckCircle_FiLoader_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLoader, {\n                    className: \"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTitle = ()=>{\n        switch(status){\n            case 'loading':\n                return 'Confirmando tu email...';\n            case 'success':\n                return '¡Email confirmado!';\n            case 'redirecting':\n                return '¡Confirmación exitosa!';\n            case 'error':\n                return 'Error en la confirmación';\n            default:\n                return 'Procesando...';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white shadow-lg rounded-lg p-8 max-w-md w-full text-center\",\n            children: [\n                getIcon(),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-semibold text-gray-800 mb-4\",\n                    children: getTitle()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: message\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/auth/login'),\n                            className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"Ir a Iniciar Sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/'),\n                            className: \"w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors\",\n                            children: \"Volver al Inicio\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfirmedPage, \"+K8UR9Z8ecpnIts7wSvQtPpnWTc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ConfirmedPage;\nvar _c;\n$RefreshReg$(_c, \"ConfirmedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/confirmed/page.tsx\n"));

/***/ })

});