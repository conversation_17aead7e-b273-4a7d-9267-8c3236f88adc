import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { stripe } from '@/lib/stripe/config';

export async function POST(request: NextRequest) {
  try {
    const { tokenAmount, price } = await request.json();

    // Validar parámetros
    if (!tokenAmount || !price) {
      return NextResponse.json({
        error: 'Parámetros requeridos: tokenAmount y price'
      }, { status: 400 });
    }

    // Validar que sea exactamente 1M tokens por 10€
    if (tokenAmount !== 1000000 || price !== 10.00) {
      return NextResponse.json({
        error: 'Solo se permite la compra de 1,000,000 tokens por 10.00€'
      }, { status: 400 });
    }

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );

    // Obtener usuario autenticado
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({
        error: 'Usuario no autenticado'
      }, { status: 401 });
    }

    // Verificar que el usuario no tenga plan gratuito
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('subscription_plan, payment_verified')
      .eq('user_id', user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({
        error: 'Perfil de usuario no encontrado'
      }, { status: 404 });
    }

    if (profile.subscription_plan === 'free') {
      return NextResponse.json({
        error: 'Los usuarios con plan gratuito no pueden comprar tokens adicionales'
      }, { status: 403 });
    }

    if (!profile.payment_verified) {
      return NextResponse.json({
        error: 'Debe tener un plan de pago verificado para comprar tokens adicionales'
      }, { status: 403 });
    }

    if (!stripe) {
      console.error('Stripe not initialized');
      return NextResponse.json({
        error: 'Error de configuración de Stripe'
      }, { status: 500 });
    }

    // Crear sesión de checkout en Stripe
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: 'Tokens Adicionales - OposiAI',
              description: '1,000,000 tokens adicionales para tu cuenta',
              images: [], // Puedes agregar una imagen del producto aquí
            },
            unit_amount: Math.round(price * 100), // Convertir a centavos
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      customer_email: user.email,
      client_reference_id: user.id,
      metadata: {
        type: 'token_purchase',
        user_id: user.id,
        token_amount: tokenAmount.toString(),
        price: price.toString(),
        created_at: new Date().toISOString(),
      },
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/app?token_purchase=success`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/app?token_purchase=cancelled`,
      automatic_tax: {
        enabled: true,
      },
      billing_address_collection: 'required',
    });

    console.log('Token purchase session created:', session.id);

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    });

  } catch (error) {
    console.error('Error creating token purchase session:', error);
    return NextResponse.json({
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
