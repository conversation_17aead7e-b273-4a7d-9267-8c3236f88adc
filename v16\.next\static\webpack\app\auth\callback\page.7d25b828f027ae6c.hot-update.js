"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n// ===== Archivo: src\\app\\auth\\callback\\page.tsx (CORREGIDO Y SIMPLIFICADO) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AuthCallbackContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Procesando autenticación, por favor espera...');\n    const [logMessages, setLogMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const timeoutIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const log = (msg, data)=>{\n        const timestamp = new Date().toISOString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(msg);\n        console.log(logEntry, data || '');\n        setLogMessages((prev)=>[\n                ...prev,\n                logEntry\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthCallbackContent.useEffect\": ()=>{\n            log('--- AuthCallbackContent useEffect INICIADO ---');\n            log('URL Completa en el cliente:', window.location.href);\n            const errorDescription = searchParams.get('error_description');\n            if (errorDescription) {\n                log('❌ Error explícito en la URL.', {\n                    error: errorDescription\n                });\n                setStatus('error');\n                setMessage(decodeURIComponent(errorDescription));\n                return;\n            }\n            const supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            log('Cliente Supabase del navegador inicializado.');\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthCallbackContent.useEffect\": async (event, session)=>{\n                    var _session_user, _session_user1;\n                    log(\"EVENTO onAuthStateChange RECIBIDO: \".concat(event), {\n                        hasSession: !!session,\n                        userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                        userMetadata: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.user_metadata\n                    });\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current); // Limpiar el timeout si se maneja un evento\n                        timeoutIdRef.current = null;\n                    }\n                    if (event === 'PASSWORD_RECOVERY') {\n                        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');\n                        setStatus('success');\n                        setMessage('Sesión de recuperación lista. Redirigiendo para establecer contraseña...');\n                        router.push('/auth/reset-password');\n                    // ===== MODIFICACIÓN START (Lógica para requires_password_setup en SIGNED_IN) =====\n                    } else if (event === 'SIGNED_IN') {\n                        log('✅ Evento SIGNED_IN detectado.');\n                        if (session === null || session === void 0 ? void 0 : session.user) {\n                            var _session_user_user_metadata;\n                            // Verificar si el usuario necesita configurar su contraseña\n                            const userRequiresPasswordSetup = ((_session_user_user_metadata = session.user.user_metadata) === null || _session_user_user_metadata === void 0 ? void 0 : _session_user_user_metadata.requires_password_setup) === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Usuario necesita establecer contraseña inicial. Redirigiendo a /auth/reset-password...');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                // /auth/reset-password manejará la sesión actual y permitirá actualizar la contraseña\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Usuario ya tiene contraseña o no requiere setup. Redirigiendo a /app...');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('⚠️ Evento SIGNED_IN sin sesión completa. Comportamiento inesperado.');\n                            setStatus('error');\n                            setMessage('Error inesperado durante el inicio de sesión. Por favor, inténtalo de nuevo.');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'USER_UPDATED' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        var _session_user_user_metadata1;\n                        // Este evento puede ser útil si la confirmación de email ocurre y luego se necesita setear contraseña.\n                        log('✅ Evento USER_UPDATED con sesión.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en USER_UPDATED) =====\n                        const userRequiresPasswordSetup = ((_session_user_user_metadata1 = session.user.user_metadata) === null || _session_user_user_metadata1 === void 0 ? void 0 : _session_user_user_metadata1.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Usuario actualizado necesita establecer contraseña. Redirigiendo a /auth/reset-password...');\n                            setStatus('success');\n                            setMessage('Actualización de cuenta completada. Crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Cuenta actualizada. Redirigiendo a /app...');\n                            setStatus('success');\n                            setMessage('Cuenta actualizada. Redirigiendo...');\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'INITIAL_SESSION' && !session) {\n                        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos o timeout.');\n                    // No hacer nada, esperar al timeout si no llegan más eventos.\n                    } else if (event === 'INITIAL_SESSION' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        var _session_user_user_metadata2;\n                        // Se encontró una sesión inicial, puede ser de una pestaña anterior o un token válido procesado rápido.\n                        log('✅ Evento INITIAL_SESSION con sesión. Procediendo como SIGNED_IN.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en INITIAL_SESSION) =====\n                        const userRequiresPasswordSetup = ((_session_user_user_metadata2 = session.user.user_metadata) === null || _session_user_user_metadata2 === void 0 ? void 0 : _session_user_user_metadata2.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            router.push('/auth/reset-password');\n                        } else {\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"]);\n            log('Listener onAuthStateChange configurado.');\n            // Verificación inmediata de la sesión (por si ya está autenticado)\n            const checkCurrentSession = {\n                \"AuthCallbackContent.useEffect.checkCurrentSession\": async ()=>{\n                    const { data: { session: currentSession } } = await supabase.auth.getSession();\n                    if ((currentSession === null || currentSession === void 0 ? void 0 : currentSession.user) && status === 'loading') {\n                        var _currentSession_user_user_metadata;\n                        log('✅ Sesión ya existente encontrada inmediatamente.');\n                        const userRequiresPasswordSetup = ((_currentSession_user_user_metadata = currentSession.user.user_metadata) === null || _currentSession_user_user_metadata === void 0 ? void 0 : _currentSession_user_user_metadata.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Redirigiendo a /auth/reset-password (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Redirigiendo a /app (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Autenticación exitosa! Redirigiendo...');\n                            router.push('/app');\n                        }\n                        if (timeoutIdRef.current) {\n                            clearTimeout(timeoutIdRef.current);\n                            timeoutIdRef.current = null;\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect.checkCurrentSession\"];\n            // Ejecutar verificación inmediata\n            checkCurrentSession();\n            // Timeout reducido para casos donde no se recibe el evento esperado\n            timeoutIdRef.current = setTimeout({\n                \"AuthCallbackContent.useEffect\": async ()=>{\n                    if (status === 'loading') {\n                        const { data: { session: finalCheckSession }, error: finalCheckError } = await supabase.auth.getSession();\n                        if (finalCheckSession === null || finalCheckSession === void 0 ? void 0 : finalCheckSession.user) {\n                            var _finalCheckSession_user_user_metadata;\n                            log('✅ Sesión encontrada en la verificación final del timeout.');\n                            const userRequiresPasswordSetup = ((_finalCheckSession_user_user_metadata = finalCheckSession.user.user_metadata) === null || _finalCheckSession_user_user_metadata === void 0 ? void 0 : _finalCheckSession_user_user_metadata.requires_password_setup) === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Redirigiendo a /auth/reset-password desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Redirigiendo a /app desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('❌ TIMEOUT (5s): No se recibió un evento de autenticación válido y no hay sesión activa.');\n                            log('Detalles del error en la verificación final del timeout:', finalCheckError === null || finalCheckError === void 0 ? void 0 : finalCheckError.message);\n                            setStatus('error');\n                            setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Si el problema persiste, intenta registrarte de nuevo o contacta a soporte.');\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"], 5000); // Reducido a 5 segundos\n            return ({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    log('--- AuthCallbackContent useEffect LIMPIEZA ---');\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    log('Suscripción a onAuthStateChange eliminada.');\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                        log('Temporizador de timeout limpiado.');\n                    }\n                }\n            })[\"AuthCallbackContent.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthCallbackContent.useEffect\"], [\n        router,\n        searchParams\n    ]);\n    // ... (resto del componente JSX sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Procesando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"\\xa1\\xc9xito!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Error de Autenticaci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/login'),\n                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                                children: \"Ir a Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackContent, \"fOlhbpKiY/OUV4HDYod1OUHDlWc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AuthCallbackContent;\nfunction AuthCallbackPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Procesando autenticaci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthCallbackContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AuthCallbackPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AuthCallbackContent\");\n$RefreshReg$(_c1, \"AuthCallbackPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback/page.tsx\n"));

/***/ })

});