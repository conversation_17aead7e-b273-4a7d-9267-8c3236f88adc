"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n// src/app/auth/callback/page.tsx\n// VERSIÓN MODIFICADA CON MANEJO MEJORADO Y FLAG PARA SETUP DE CONTRASEÑA\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n // Añadir useRef\n\n\nfunction AuthCallbackContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Procesando autenticación, por favor espera...');\n    const [logMessages, setLogMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const timeoutIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // Usar ref para el ID del timeout\n    // Función para añadir logs al estado y a la consola\n    const log = (msg, data)=>{\n        const timestamp = new Date().toISOString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(msg);\n        console.log(logEntry, data || '');\n        setLogMessages((prev)=>[\n                ...prev,\n                logEntry\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthCallbackContent.useEffect\": ()=>{\n            log('=== [AUTH-CALLBACK-PAGE] AuthCallbackContent useEffect INICIADO ===');\n            // Log inicial de la URL para ver qué llega\n            log('🌐 [AUTH-CALLBACK-PAGE] URL Completa en el cliente:', window.location.href);\n            log('📋 [AUTH-CALLBACK-PAGE] Query Params (searchParams):', Object.fromEntries(searchParams.entries()));\n            log('🔍 [AUTH-CALLBACK-PAGE] Verificando si llegamos desde el servidor o directamente...');\n            const errorDescription = searchParams.get('error_description');\n            const errorCode = searchParams.get('error_code');\n            const errorParam = searchParams.get('error');\n            if (errorDescription || errorCode || errorParam) {\n                let friendlyMessage = decodeURIComponent(errorDescription || errorParam || 'Error desconocido');\n                if (errorCode === 'user_already_invited' || errorDescription && errorDescription.includes('User already invited')) {\n                    friendlyMessage = 'Ya se ha enviado una invitación a este correo. Por favor, revisa tu bandeja de entrada (y spam). Si no la encuentras, puedes intentar recuperar tu contraseña desde la página de login.';\n                } else if (errorCode === 'token_expired_or_invalid' || errorDescription && (errorDescription.includes('invalid token') || errorDescription.includes('expired'))) {\n                    friendlyMessage = 'El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta registrarte o iniciar sesión de nuevo.';\n                } else if (friendlyMessage.includes('Código de autenticación no encontrado')) {\n                    friendlyMessage = 'El enlace de confirmación no es válido. Por favor, verifica que hayas copiado la URL completa del email.';\n                } else if (friendlyMessage.includes('No se pudo completar la autenticación')) {\n                    friendlyMessage = 'No se pudo completar la verificación de tu email. Por favor, intenta usar el enlace del email nuevamente.';\n                }\n                log('❌ Error explícito detectado en la URL.', {\n                    error: friendlyMessage,\n                    code: errorCode,\n                    error_param: errorParam\n                });\n                setStatus('error');\n                setMessage(friendlyMessage);\n                if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current); // Limpiar timeout si hay error\n                return;\n            }\n            const supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            log('Cliente Supabase del navegador inicializado.');\n            // Verificar si hay código en la URL y dar tiempo a Supabase para procesarlo\n            const code = searchParams.get('code');\n            if (code) {\n                log('🔑 [AUTH-CALLBACK-PAGE] Código encontrado en URL:', code.substring(0, 10) + '...');\n                log('⏳ [AUTH-CALLBACK-PAGE] Esperando que Supabase procese automáticamente el código...');\n                // Dar tiempo a Supabase para procesar el código automáticamente\n                setTimeout({\n                    \"AuthCallbackContent.useEffect\": ()=>{\n                        supabase.auth.getSession().then({\n                            \"AuthCallbackContent.useEffect\": (param)=>{\n                                let { data: { session } } = param;\n                                if (session) {\n                                    log('✅ [AUTH-CALLBACK-PAGE] Sesión encontrada después del procesamiento automático');\n                                // El listener onAuthStateChange debería detectar esto\n                                } else {\n                                    log('⚠️ [AUTH-CALLBACK-PAGE] No hay sesión después del procesamiento automático, verificando...');\n                                // Si no hay sesión, el listener INITIAL_SESSION lo manejará\n                                }\n                            }\n                        }[\"AuthCallbackContent.useEffect\"]);\n                    }\n                }[\"AuthCallbackContent.useEffect\"], 1000); // Dar 1 segundo para que Supabase procese\n            }\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthCallbackContent.useEffect\": async (event, session)=>{\n                    var _session_user, _session_user1;\n                    log(\"EVENTO onAuthStateChange RECIBIDO: \".concat(event), {\n                        hasSession: !!session,\n                        userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                        userMetadata: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.user_metadata\n                    });\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current); // Limpiar el timeout si se maneja un evento\n                        timeoutIdRef.current = null;\n                    }\n                    if (event === 'PASSWORD_RECOVERY') {\n                        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');\n                        setStatus('success');\n                        setMessage('Sesión de recuperación lista. Redirigiendo para establecer contraseña...');\n                        router.push('/auth/reset-password');\n                    // ===== MODIFICACIÓN START (Lógica para requires_password_setup en SIGNED_IN) =====\n                    } else if (event === 'SIGNED_IN') {\n                        log('✅ Evento SIGNED_IN detectado.');\n                        if (session === null || session === void 0 ? void 0 : session.user) {\n                            var _session_user_user_metadata;\n                            // Verificar si el usuario necesita configurar su contraseña\n                            const userRequiresPasswordSetup = ((_session_user_user_metadata = session.user.user_metadata) === null || _session_user_user_metadata === void 0 ? void 0 : _session_user_user_metadata.requires_password_setup) === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Usuario necesita establecer contraseña inicial. Redirigiendo a /auth/reset-password...');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                // /auth/reset-password manejará la sesión actual y permitirá actualizar la contraseña\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Usuario ya tiene contraseña o no requiere setup. Redirigiendo a /app...');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('⚠️ Evento SIGNED_IN sin sesión completa. Comportamiento inesperado.');\n                            setStatus('error');\n                            setMessage('Error inesperado durante el inicio de sesión. Por favor, inténtalo de nuevo.');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'USER_UPDATED' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        var _session_user_user_metadata1;\n                        // Este evento puede ser útil si la confirmación de email ocurre y luego se necesita setear contraseña.\n                        log('✅ Evento USER_UPDATED con sesión.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en USER_UPDATED) =====\n                        const userRequiresPasswordSetup = ((_session_user_user_metadata1 = session.user.user_metadata) === null || _session_user_user_metadata1 === void 0 ? void 0 : _session_user_user_metadata1.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Usuario actualizado necesita establecer contraseña. Redirigiendo a /auth/reset-password...');\n                            setStatus('success');\n                            setMessage('Actualización de cuenta completada. Crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Cuenta actualizada. Redirigiendo a /app...');\n                            setStatus('success');\n                            setMessage('Cuenta actualizada. Redirigiendo...');\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'INITIAL_SESSION' && !session) {\n                        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos o timeout.');\n                    // No hacer nada, esperar al timeout si no llegan más eventos.\n                    } else if (event === 'INITIAL_SESSION' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        var _session_user_user_metadata2;\n                        // Se encontró una sesión inicial, puede ser de una pestaña anterior o un token válido procesado rápido.\n                        log('✅ Evento INITIAL_SESSION con sesión. Procediendo como SIGNED_IN.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en INITIAL_SESSION) =====\n                        const userRequiresPasswordSetup = ((_session_user_user_metadata2 = session.user.user_metadata) === null || _session_user_user_metadata2 === void 0 ? void 0 : _session_user_user_metadata2.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            router.push('/auth/reset-password');\n                        } else {\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"]);\n            log('Listener onAuthStateChange configurado.');\n            // Verificación inmediata de la sesión (por si ya está autenticado)\n            const checkCurrentSession = {\n                \"AuthCallbackContent.useEffect.checkCurrentSession\": async ()=>{\n                    const { data: { session: currentSession } } = await supabase.auth.getSession();\n                    if ((currentSession === null || currentSession === void 0 ? void 0 : currentSession.user) && status === 'loading') {\n                        var _currentSession_user_user_metadata;\n                        log('✅ Sesión ya existente encontrada inmediatamente.');\n                        const userRequiresPasswordSetup = ((_currentSession_user_user_metadata = currentSession.user.user_metadata) === null || _currentSession_user_user_metadata === void 0 ? void 0 : _currentSession_user_user_metadata.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Redirigiendo a /auth/reset-password (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Redirigiendo a /app (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Autenticación exitosa! Redirigiendo...');\n                            router.push('/app');\n                        }\n                        if (timeoutIdRef.current) {\n                            clearTimeout(timeoutIdRef.current);\n                            timeoutIdRef.current = null;\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect.checkCurrentSession\"];\n            // Ejecutar verificación inmediata\n            checkCurrentSession();\n            // Timeout reducido para casos donde no se recibe el evento esperado\n            timeoutIdRef.current = setTimeout({\n                \"AuthCallbackContent.useEffect\": async ()=>{\n                    if (status === 'loading') {\n                        const { data: { session: finalCheckSession }, error: finalCheckError } = await supabase.auth.getSession();\n                        if (finalCheckSession === null || finalCheckSession === void 0 ? void 0 : finalCheckSession.user) {\n                            var _finalCheckSession_user_user_metadata;\n                            log('✅ Sesión encontrada en la verificación final del timeout.');\n                            const userRequiresPasswordSetup = ((_finalCheckSession_user_user_metadata = finalCheckSession.user.user_metadata) === null || _finalCheckSession_user_user_metadata === void 0 ? void 0 : _finalCheckSession_user_user_metadata.requires_password_setup) === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Redirigiendo a /auth/reset-password desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Redirigiendo a /app desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('❌ TIMEOUT (5s): No se recibió un evento de autenticación válido y no hay sesión activa.');\n                            log('Detalles del error en la verificación final del timeout:', finalCheckError === null || finalCheckError === void 0 ? void 0 : finalCheckError.message);\n                            setStatus('error');\n                            setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Si el problema persiste, intenta registrarte de nuevo o contacta a soporte.');\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"], 5000); // Reducido a 5 segundos\n            return ({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    log('--- AuthCallbackContent useEffect LIMPIEZA ---');\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    log('Suscripción a onAuthStateChange eliminada.');\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                        log('Temporizador de timeout limpiado.');\n                    }\n                }\n            })[\"AuthCallbackContent.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthCallbackContent.useEffect\"], [\n        router,\n        searchParams\n    ]);\n    // ... (resto del componente JSX sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Procesando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"\\xa1\\xc9xito!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Error de Autenticaci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/login'),\n                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                                children: \"Ir a Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackContent, \"fOlhbpKiY/OUV4HDYod1OUHDlWc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AuthCallbackContent;\nfunction AuthCallbackPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Procesando autenticaci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthCallbackContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AuthCallbackPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AuthCallbackContent\");\n$RefreshReg$(_c1, \"AuthCallbackPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback/page.tsx\n"));

/***/ })

});