/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register-free/route";
exports.ids = ["app/api/auth/register-free/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_register_free_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/register-free/route.ts */ \"(rsc)/./src/app/api/auth/register-free/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register-free/route\",\n        pathname: \"/api/auth/register-free\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register-free/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\auth\\\\register-free\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_register_free_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/register-free/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/register-free/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n// ===== Archivo: src\\app\\api\\auth\\register-free\\route.ts =====\n// src/app/api/auth/register-free/route.ts\n// Endpoint para registro de usuarios gratuitos con confirmación de email\n\n\n// Rate limiting para prevenir spam\nconst registrationAttempts = new Map();\nconst RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutos\nconst MAX_ATTEMPTS = 3;\nasync function POST(request) {\n    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';\n    try {\n        const { email, password, customerName } = await request.json();\n        // Validación básica\n        if (!email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email y contraseña son requeridos'\n            }, {\n                status: 400\n            });\n        }\n        // Rate limiting\n        const now = Date.now();\n        const attempts = registrationAttempts.get(clientIP);\n        if (attempts && attempts.count >= MAX_ATTEMPTS && now - attempts.lastAttempt < RATE_LIMIT_WINDOW) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Demasiados intentos de registro. Intenta de nuevo en 15 minutos.'\n            }, {\n                status: 429\n            });\n        }\n        // Supabase manejará automáticamente la validación de email duplicado\n        // Crear el usuario usando el cliente del servidor para generar códigos de confirmación correctos\n        console.log('🔄 [REGISTER-FREE] Iniciando signUp con createServerSupabaseClient...');\n        console.log('🔄 [REGISTER-FREE] Configuración:', {\n            email,\n            hasPassword: !!password,\n            customerName,\n            emailRedirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            siteUrl: \"http://localhost:3000\"\n        });\n        // Usar el cliente del servidor para generar códigos en lugar de tokens\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        const { data: { user }, error: signUpError } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    name: customerName || email.split('@')[0],\n                    plan: 'free'\n                },\n                emailRedirectTo: `${\"http://localhost:3000\"}/auth/callback`\n            }\n        });\n        console.log('📊 [REGISTER-FREE] Resultado de signUp (createServerSupabaseClient):', {\n            hasUser: !!user,\n            userId: user?.id,\n            userEmail: user?.email,\n            userConfirmed: user?.email_confirmed_at,\n            hasError: !!signUpError,\n            errorMessage: signUpError?.message,\n            errorCode: signUpError?.status,\n            clientType: 'createServerSupabaseClient'\n        });\n        if (signUpError) {\n            console.error('Error en signUp:', signUpError);\n            // Actualizar rate limiting\n            registrationAttempts.set(clientIP, {\n                count: (attempts?.count || 0) + 1,\n                lastAttempt: now\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: signUpError.message\n            }, {\n                status: 400\n            });\n        }\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No se pudo crear el usuario.'\n            }, {\n                status: 500\n            });\n        }\n        // NOTA: No crear perfil inmediatamente porque el usuario aún no está confirmado\n        // El perfil se creará después de la confirmación del email mediante trigger o callback\n        console.log('⏳ [REGISTER-FREE] Perfil se creará después de la confirmación del email');\n        console.log('📧 [REGISTER-FREE] Usuario debe confirmar email para completar el registro');\n        // TODO: Implementar creación de perfil después de confirmación de email\n        // Opciones:\n        // 1. Trigger de base de datos cuando auth.users se actualiza\n        // 2. Crear perfil en el callback después de confirmación exitosa\n        // Limpiar rate limiting en caso de éxito\n        registrationAttempts.delete(clientIP);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Registro exitoso. Revisa tu email para confirmar tu cuenta.',\n            userId: user.id\n        });\n    } catch (error) {\n        console.error('Error en registro gratuito:', error);\n        // Actualizar rate limiting\n        registrationAttempts.set(clientIP, {\n            count: (registrationAttempts.get(clientIP)?.count || 0) + 1,\n            lastAttempt: Date.now()\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor. Por favor, intenta de nuevo.'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/register-free/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// Cliente para el servidor (componentes del servidor, API routes)\nasync function createServerSupabaseClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: false,\n            autoRefreshToken: false,\n            detectSessionInUrl: false // No detectar sesión en URL\n        },\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    // Filtrar cookies de autenticación para no persistirlas\n                    const filteredCookies = cookiesToSet.filter((cookie)=>!cookie.name.includes('auth-token') && !cookie.name.includes('refresh-token'));\n                    filteredCookies.forEach(({ name, value, options })=>cookieStore.set(name, value, {\n                            ...options,\n                            maxAge: undefined,\n                            expires: undefined // No establecer expires para evitar persistencia\n                        }));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();