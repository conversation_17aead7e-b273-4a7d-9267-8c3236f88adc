"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n// ===== Archivo: src\\app\\auth\\callback\\page.tsx (CORREGIDO Y SIMPLIFICADO) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AuthCallbackContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Procesando autenticación, por favor espera...');\n    const [logMessages, setLogMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const timeoutIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const log = (msg, data)=>{\n        const timestamp = new Date().toISOString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(msg);\n        console.log(logEntry, data || '');\n        setLogMessages((prev)=>[\n                ...prev,\n                logEntry\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthCallbackContent.useEffect\": ()=>{\n            log('--- AuthCallbackContent useEffect INICIADO ---');\n            log('URL Completa en el cliente:', window.location.href);\n            const errorDescription = searchParams.get('error_description');\n            if (errorDescription) {\n                log('❌ Error explícito en la URL.', {\n                    error: errorDescription\n                });\n                setStatus('error');\n                setMessage(decodeURIComponent(errorDescription));\n                return;\n            }\n            const supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            log('Cliente Supabase del navegador inicializado.');\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthCallbackContent.useEffect\": async (event, session)=>{\n                    var _session_user, _session_user1;\n                    log(\"EVENTO onAuthStateChange RECIBIDO: \".concat(event), {\n                        hasSession: !!session,\n                        userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                        userEmail: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email\n                    });\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                        timeoutIdRef.current = null;\n                    }\n                    if (event === 'SIGNED_IN' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        log('✅ Evento SIGNED_IN detectado. Redirigiendo a /app...');\n                        setStatus('success');\n                        setMessage('¡Autenticación exitosa! Redirigiendo...');\n                        router.push('/app');\n                    } else if (event === 'PASSWORD_RECOVERY') {\n                        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');\n                        setStatus('success');\n                        setMessage('Sesión de recuperación lista. Redirigiendo...');\n                        router.push('/auth/reset-password');\n                    } else if (event === 'USER_UPDATED' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        log('✅ Evento USER_UPDATED con sesión. Redirigiendo a /app...');\n                        setStatus('success');\n                        setMessage('Cuenta actualizada. Redirigiendo...');\n                        router.push('/app');\n                    } else if (event === 'INITIAL_SESSION' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        log('✅ Evento INITIAL_SESSION con sesión válida. Redirigiendo a /app...');\n                        setStatus('success');\n                        setMessage('¡Sesión encontrada! Redirigiendo...');\n                        router.push('/app');\n                    } else if (event === 'INITIAL_SESSION' && !session) {\n                        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos...');\n                        // Verificar inmediatamente si hay una sesión\n                        setTimeout({\n                            \"AuthCallbackContent.useEffect\": async ()=>{\n                                const { data: { session: currentSession } } = await supabase.auth.getSession();\n                                if ((currentSession === null || currentSession === void 0 ? void 0 : currentSession.user) && status === 'loading') {\n                                    log('✅ Sesión encontrada en verificación posterior. Redirigiendo...');\n                                    setStatus('success');\n                                    setMessage('¡Sesión encontrada! Redirigiendo...');\n                                    router.push('/app');\n                                }\n                            }\n                        }[\"AuthCallbackContent.useEffect\"], 1000);\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"]);\n            log('Listener onAuthStateChange configurado. Esperando evento...');\n            // Iniciar un timeout de seguridad más largo (10-15s) para manejar casos raros\n            timeoutIdRef.current = setTimeout({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    if (status === 'loading') {\n                        log('❌ TIMEOUT (10s): No se recibió un evento de autenticación válido.');\n                        setStatus('error');\n                        setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta de nuevo.');\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"], 10000); // 10 segundos\n            return ({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    log('--- AuthCallbackContent useEffect LIMPIEZA ---');\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                    }\n                }\n            })[\"AuthCallbackContent.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthCallbackContent.useEffect\"], [\n        router\n    ]);\n    // ... (resto del componente JSX sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Procesando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"\\xa1\\xc9xito!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Error de Autenticaci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/login'),\n                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                                children: \"Ir a Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 pt-4 border-t border-gray-200 text-left bg-gray-50 p-3 rounded max-h-48 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-700 mb-2\",\n                            children: \"Registro de Eventos:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 space-y-1\",\n                            children: logMessages.map((logMsg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: logMsg\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 53\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackContent, \"fOlhbpKiY/OUV4HDYod1OUHDlWc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AuthCallbackContent;\nfunction AuthCallbackPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Procesando autenticaci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthCallbackContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AuthCallbackPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AuthCallbackContent\");\n$RefreshReg$(_c1, \"AuthCallbackPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback/page.tsx\n"));

/***/ })

});