// src/__tests__/test-utils.tsx
// Utilidades de testing para la aplicación

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { AuthProvider } from '@/contexts/AuthContext';
import { User } from '@supabase/supabase-js';

// Mock de usuario para tests
export const mockUser: User = {
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: {},
  app_metadata: {},
  aud: 'authenticated',
  created_at: '2025-01-01T00:00:00.000Z',
  updated_at: '2025-01-01T00:00:00.000Z',
  email_confirmed_at: '2025-01-01T00:00:00.000Z',
  last_sign_in_at: '2025-01-01T00:00:00.000Z',
  role: 'authenticated',
  confirmation_sent_at: '2025-01-01T00:00:00.000Z'
};

// Mock de perfil de usuario
export const mockUserProfile = {
  user_id: 'test-user-id',
  subscription_plan: 'free' as const,
  payment_verified: false,
  monthly_token_limit: 50000,
  current_month_tokens: 1000,
  current_month: '2025-01-01',
  created_at: '2025-01-01T00:00:00.000Z',
  updated_at: '2025-01-01T00:00:00.000Z'
};

// Configuración de contexto de autenticación para tests
interface AuthContextValue {
  user: User | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const mockAuthContext: AuthContextValue = {
  user: mockUser,
  loading: false,
  signOut: jest.fn().mockResolvedValue(undefined),
  refreshUser: jest.fn().mockResolvedValue(undefined)
};

// Provider personalizado para tests
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  authContext?: Partial<AuthContextValue>;
}

const AllTheProviders: React.FC<{ children: React.ReactNode; authContext?: Partial<AuthContextValue> }> = ({ 
  children, 
  authContext = {} 
}) => {
  const contextValue = { ...mockAuthContext, ...authContext };
  
  return (
    <AuthProvider value={contextValue}>
      {children}
    </AuthProvider>
  );
};

// Función de render personalizada
const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { authContext, ...renderOptions } = options;
  
  return render(ui, {
    wrapper: ({ children }) => (
      <AllTheProviders authContext={authContext}>
        {children}
      </AllTheProviders>
    ),
    ...renderOptions,
  });
};

// Utilidades para mocking de servicios
export const createMockSupabaseAdmin = () => ({
  getUserProfile: jest.fn(),
  upsertUserProfile: jest.fn(),
  updateUserProfile: jest.fn(),
  deleteUser: jest.fn(),
  createUser: jest.fn(),
  listUsers: jest.fn(),
  getUserByEmail: jest.fn(),
  updateUserMetadata: jest.fn(),
  sendPasswordResetEmail: jest.fn(),
  verifyUserEmail: jest.fn(),
  revokeUserSessions: jest.fn()
});

export const createMockLimitHandler = () => ({
  checkUserLimits: jest.fn(),
  isActionBlocked: jest.fn(),
  recordUsage: jest.fn(),
  createLimitNotification: jest.fn(),
  getUserLimitStatus: jest.fn(),
  resetMonthlyUsage: jest.fn()
});

export const createMockPermissionService = () => ({
  checkPermission: jest.fn(),
  checkMultiplePermissions: jest.fn(),
  createFeaturePermission: jest.fn(),
  validateUserAccess: jest.fn(),
  getFeatureRequirements: jest.fn()
});

export const createMockPlanValidationService = () => ({
  validateFeatureAccess: jest.fn(),
  canUserPerformAction: jest.fn(),
  updateTokenUsage: jest.fn(),
  getUserAccessInfo: jest.fn(),
  checkUpgradeNeeded: jest.fn(),
  getPlanLimits: jest.fn(),
  isFeatureAvailable: jest.fn()
});

// Utilidades para testing de hooks
export const createMockAuthHook = (overrides: Partial<AuthContextValue> = {}) => ({
  user: mockUser,
  loading: false,
  signOut: jest.fn().mockResolvedValue(undefined),
  refreshUser: jest.fn().mockResolvedValue(undefined),
  ...overrides
});

// Utilidades para testing de formularios
export const fillForm = async (getByLabelText: any, formData: Record<string, string>) => {
  for (const [label, value] of Object.entries(formData)) {
    const input = getByLabelText(label);
    await userEvent.type(input, value);
  }
};

// Utilidades para testing de API calls
export const createMockApiResponse = <T,>(data: T, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: jest.fn().mockResolvedValue(data),
  text: jest.fn().mockResolvedValue(JSON.stringify(data))
});

// Utilidades para testing de errores
export const createMockError = (message: string, code?: string) => {
  const error = new Error(message);
  if (code) {
    (error as any).code = code;
  }
  return error;
};

// Utilidades para testing de fechas
export const createMockDate = (dateString: string) => {
  const mockDate = new Date(dateString);
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
  return mockDate;
};

// Utilidades para testing de localStorage
export const createMockLocalStorage = () => {
  const store: Record<string, string> = {};
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    })
  };
};

// Utilidades para testing de timers
export const advanceTimersByTime = (ms: number) => {
  jest.advanceTimersByTime(ms);
};

export const runAllTimers = () => {
  jest.runAllTimers();
};

// Utilidades para testing de promesas
export const waitForNextTick = () => new Promise(resolve => setImmediate(resolve));

// Utilidades para testing de componentes async
export const waitForLoadingToFinish = async () => {
  await waitFor(() => {
    expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
  });
};

// Re-export everything
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
export { customRender as render };

// Export tipos útiles
export type { CustomRenderOptions };

// Constantes útiles para tests
export const TEST_CONSTANTS = {
  TIMEOUT: 5000,
  DEBOUNCE_DELAY: 300,
  API_DELAY: 100,
  ANIMATION_DURATION: 200
} as const;

// Matchers personalizados para Jest
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveAttribute(attr: string, value?: string): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toHaveValue(value: string | number): R;
    }
  }
}
