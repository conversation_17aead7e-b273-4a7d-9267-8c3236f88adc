// ===== Archivo: src\app\api\auth\register-free\route.ts =====
// src/app/api/auth/register-free/route.ts
// Endpoint para registro de usuarios gratuitos con confirmación de email

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getTokenLimitForPlan } from '@/config/plans';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// Rate limiting para prevenir spam
const registrationAttempts = new Map<string, { count: number; lastAttempt: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutos
const MAX_ATTEMPTS = 3;

export async function POST(request: NextRequest) {
  const clientIP = request.headers.get('x-forwarded-for') || 'unknown';

  try {
    const { email, password, customerName } = await request.json();

    // Validación básica
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email y contraseña son requeridos' },
        { status: 400 }
      );
    }

    // Rate limiting
    const now = Date.now();
    const attempts = registrationAttempts.get(clientIP);

    if (attempts && attempts.count >= MAX_ATTEMPTS && (now - attempts.lastAttempt) < RATE_LIMIT_WINDOW) {
      return NextResponse.json(
        { error: 'Demasiados intentos de registro. Intenta de nuevo en 15 minutos.' },
        { status: 429 }
      );
    }

    // Supabase manejará automáticamente la validación de email duplicado

    // Crear el usuario usando el cliente del servidor
    const supabase = await createServerSupabaseClient();
    const { data: { user }, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: customerName || email.split('@')[0],
          plan: 'free'
        },
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`
      }
    });

    if (signUpError) {
      console.error('Error en signUp:', signUpError);

      // Actualizar rate limiting
      registrationAttempts.set(clientIP, {
        count: (attempts?.count || 0) + 1,
        lastAttempt: now
      });

      return NextResponse.json({ error: signUpError.message }, { status: 400 });
    }

    if (!user) {
      return NextResponse.json({ error: 'No se pudo crear el usuario.' }, { status: 500 });
    }

    // Crear el perfil del usuario usando la función RPC
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01'; // YYYY-MM-DD
    const profileData = {
      subscription_plan: 'free',
      monthly_token_limit: getTokenLimitForPlan('free'),
      current_month_tokens: 0,
      current_month: currentMonth,
      payment_verified: true, // Para cuentas gratuitas, se considera verificado
      auto_renew: false,
      plan_features: ['basic_chat', 'limited_tokens'],
      security_flags: {
        created_via_free_registration: true,
        registration_date: new Date().toISOString(),
        email_confirmation_required: true
      }
    };

    const { error: rpcError } = await supabaseAdmin
      .rpc('create_user_profile_and_history', {
        p_user_id: user.id,
        p_transaction_id: null,
        p_profile_data: profileData
      });

    if (rpcError) {
      console.error('Error creando perfil:', rpcError);
      // No fallar completamente, el perfil se puede crear después
    }

    // Limpiar rate limiting en caso de éxito
    registrationAttempts.delete(clientIP);

    return NextResponse.json({
      success: true,
      message: 'Registro exitoso. Revisa tu email para confirmar tu cuenta.',
      userId: user.id
    });

  } catch (error) {
    console.error('Error en registro gratuito:', error);

    // Actualizar rate limiting
    registrationAttempts.set(clientIP, {
      count: (registrationAttempts.get(clientIP)?.count || 0) + 1,
      lastAttempt: Date.now()
    });

    return NextResponse.json(
      { error: 'Error interno del servidor. Por favor, intenta de nuevo.' },
      { status: 500 }
    );
  }
}