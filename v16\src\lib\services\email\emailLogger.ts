// src/lib/services/email/emailLogger.ts
// Logging y tracking de notificaciones en base de datos

import { supabaseAdmin } from '@/lib/supabase/admin';
import { EmailNotification, EmailLogData, EmailUpdateData, UserNotificationsResult, EmailStatus } from './types';

export class EmailLogger {
  
  /**
   * Registrar notificación en base de datos para tracking
   */
  static async logEmailNotification(
    notification: EmailNotification, 
    status: EmailStatus = 'sent'
  ): Promise<string | null> {
    try {
      const insertData: EmailLogData = {
        recipient_email: notification.to,
        subject: notification.subject,
        type: notification.type,
        sent_at: new Date().toISOString(),
        status: status
      };

      // Agregar user_id si está disponible
      if (notification.userId) {
        insertData.user_id = notification.userId;
      }

      // Agregar metadata si está disponible
      if (notification.metadata) {
        insertData.metadata = notification.metadata;
      }

      const { data, error } = await supabaseAdmin
        .from('email_notifications')
        .insert(insertData)
        .select('id')
        .single();

      if (error) {
        throw error;
      }
        
      console.log('📝 Email notification logged:', {
        id: data.id,
        type: notification.type,
        recipient: notification.to,
        status: status,
        userId: notification.userId || 'N/A'
      });

      return data.id;
    } catch (error) {
      console.error('Error logging email notification:', error);
      // No lanzar error, es solo para tracking
      return null;
    }
  }

  /**
   * Actualizar estado de una notificación existente
   */
  static async updateEmailNotificationStatus(
    notificationId: string,
    status: EmailStatus,
    errorMessage?: string
  ): Promise<void> {
    try {
      const updateData: EmailUpdateData = {
        status: status,
        updated_at: new Date().toISOString()
      };

      // Si es un fallo, agregar el mensaje de error a metadata
      if (status === 'failed' && errorMessage) {
        updateData.metadata = {
          error_message: errorMessage,
          failed_at: new Date().toISOString()
        };
      }

      // Si es exitoso, marcar como entregado
      if (status === 'sent') {
        updateData.delivered_at = new Date().toISOString();
      }

      const { error } = await supabaseAdmin
        .from('email_notifications')
        .update(updateData)
        .eq('id', notificationId);

      if (error) {
        throw error;
      }

      console.log('📝 Email notification status updated:', {
        id: notificationId,
        status: status,
        error: errorMessage || 'N/A'
      });

    } catch (error) {
      console.error('Error updating email notification status:', error);
      // No lanzar error, es solo para tracking
    }
  }

  /**
   * Obtener historial de notificaciones de un usuario
   */
  static async getUserNotifications(
    userId: string,
    limit: number = 50,
    type?: string
  ): Promise<UserNotificationsResult> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('sent_at', { ascending: false });

      if (type) {
        query = query.eq('type', type);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw error;
      }

      // Obtener total de notificaciones
      let countQuery = supabaseAdmin
        .from('email_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (type) {
        countQuery = countQuery.eq('type', type);
      }

      const { count } = await countQuery;

      return {
        notifications: notifications || [],
        total: count || 0
      };

    } catch (error) {
      console.error('Error obteniendo notificaciones del usuario:', error);
      return {
        notifications: [],
        total: 0
      };
    }
  }

  /**
   * Obtener notificaciones fallidas para reintentos
   */
  static async getFailedNotifications(
    maxAge: number = 24, // Máximo 24 horas de antigüedad
    limit: number = 10   // Máximo 10 por consulta
  ): Promise<any[]> {
    try {
      const cutoffDate = new Date(Date.now() - maxAge * 60 * 60 * 1000).toISOString();
      
      const { data: failedNotifications, error } = await supabaseAdmin
        .from('email_notifications')
        .select('*')
        .eq('status', 'failed')
        .gte('sent_at', cutoffDate)
        .limit(limit);

      if (error) {
        throw error;
      }

      return failedNotifications || [];

    } catch (error) {
      console.error('Error obteniendo notificaciones fallidas:', error);
      return [];
    }
  }

  /**
   * Marcar notificación como reintentada
   */
  static async markAsRetried(
    originalNotificationId: string,
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    try {
      const status = success ? 'retried_successfully' : 'failed';
      const message = success ? 'Successfully retried' : errorMessage || 'Retry failed';

      await this.updateEmailNotificationStatus(
        originalNotificationId,
        status,
        message
      );

    } catch (error) {
      console.error('Error marcando notificación como reintentada:', error);
    }
  }

  /**
   * Limpiar notificaciones antiguas (para mantenimiento)
   */
  static async cleanupOldNotifications(
    daysToKeep: number = 90
  ): Promise<{
    deleted: number;
    error?: string;
  }> {
    try {
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();
      
      console.log(`🧹 Limpiando notificaciones anteriores a: ${cutoffDate}`);

      const { data, error } = await supabaseAdmin
        .from('email_notifications')
        .delete()
        .lt('sent_at', cutoffDate)
        .select('id');

      if (error) {
        throw error;
      }

      const deletedCount = data?.length || 0;
      console.log(`✅ Limpieza completada: ${deletedCount} notificaciones eliminadas`);

      return {
        deleted: deletedCount
      };

    } catch (error) {
      console.error('Error en limpieza de notificaciones:', error);
      return {
        deleted: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
