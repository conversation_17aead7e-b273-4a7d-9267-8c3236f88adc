"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config/constants.ts":
/*!*********************************!*\
  !*** ./src/config/constants.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* binding */ AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* binding */ AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FILE_LIMITS: () => (/* binding */ FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* binding */ FREE_PLAN_LIMITS),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* binding */ NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* binding */ PAYMENT_STATES),\n/* harmony export */   PRICING: () => (/* binding */ PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* binding */ PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* binding */ PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* binding */ RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* binding */ REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* binding */ RETRY_CONFIG),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* binding */ SECURITY_RISK_SCORES),\n/* harmony export */   SEVERITY_LEVELS: () => (/* binding */ SEVERITY_LEVELS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* binding */ TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* binding */ TIMEOUTS),\n/* harmony export */   TOKEN_LIMITS: () => (/* binding */ TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* binding */ VALIDATION_MESSAGES)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// src/config/constants.ts\n// Constantes centralizadas del sistema\n// ============================================================================\n// CONSTANTES DE APLICACIÓN\n// ============================================================================\n/**\n * URLs y rutas de la aplicación\n */ const APP_URLS = {\n    BASE: \"http://localhost:3001\" || 0,\n    SITE: \"http://localhost:3001\" || 0,\n    UPGRADE_PLAN: '/upgrade-plan',\n    THANK_YOU: '/thank-you',\n    LOGIN: '/login',\n    DASHBOARD: '/app',\n    PROFILE: '/profile',\n    WELCOME: '/welcome'\n};\n/**\n * Rutas públicas (no requieren autenticación)\n */ const PUBLIC_ROUTES = [\n    '/',\n    '/login',\n    '/payment',\n    '/thank-you',\n    '/contact',\n    '/privacy',\n    '/terms',\n    '/auth/callback',\n    '/auth/confirmed',\n    '/auth/unauthorized',\n    '/auth/reset-password',\n    '/auth/confirm-reset',\n    '/api/auth/register-free',\n    '/api/auth/pre-register-paid',\n    '/api/stripe/webhook',\n    '/api/stripe/create-checkout-session',\n    '/api/stripe/create-token-checkout',\n    '/api/notify-signup',\n    '/api/user/status',\n    '/api/health',\n    '/api/auth/initiate-password-setup'\n];\n/**\n * Rutas que requieren autenticación básica\n */ const AUTHENTICATED_ROUTES = [\n    '/app',\n    '/dashboard',\n    '/profile',\n    '/welcome',\n    '/upgrade-plan'\n];\n// ============================================================================\n// CONSTANTES DE VALIDACIÓN Y LÍMITES\n// ============================================================================\n/**\n * Límites de archivos y uploads\n */ const FILE_LIMITS = {\n    MAX_SIZE_MB: 5,\n    MAX_SIZE_BYTES: 5 * 1024 * 1024,\n    ALLOWED_TYPES: [\n        'application/pdf'\n    ],\n    ALLOWED_EXTENSIONS: [\n        '.pdf'\n    ]\n};\n/**\n * Límites de texto y contenido\n */ const TEXT_LIMITS = {\n    MIN_PASSWORD_LENGTH: 8,\n    MAX_PASSWORD_LENGTH: 128,\n    MAX_DOCUMENT_TITLE_LENGTH: 200,\n    MAX_DESCRIPTION_LENGTH: 500,\n    MIN_CONTENT_LENGTH: 100,\n    MAX_CONTENT_LENGTH: 50000\n};\n/**\n * Límites de tokens y uso\n */ const TOKEN_LIMITS = {\n    DEFAULT_FREE_LIMIT: 50000,\n    WARNING_THRESHOLD_PERCENTAGE: 80,\n    CRITICAL_THRESHOLD_PERCENTAGE: 90,\n    EXCEEDED_THRESHOLD_PERCENTAGE: 100\n};\n/**\n * Límites de rate limiting\n */ const RATE_LIMITS = {\n    DEFAULT_WINDOW_MINUTES: 60,\n    DEFAULT_MAX_REQUESTS: 100,\n    API_REQUESTS_PER_MINUTE: 60,\n    UPLOAD_REQUESTS_PER_HOUR: 10,\n    AUTH_ATTEMPTS_PER_HOUR: 5\n};\n// ============================================================================\n// CONSTANTES DE TIEMPO Y CONFIGURACIÓN\n// ============================================================================\n/**\n * Timeouts y intervalos\n */ const TIMEOUTS = {\n    SESSION_TIMEOUT_MS: 5 * 60 * 1000,\n    API_TIMEOUT_MS: 30 * 1000,\n    UPLOAD_TIMEOUT_MS: 60 * 1000,\n    RETRY_DELAY_MS: 1000,\n    POLLING_INTERVAL_MS: 2000 // 2 segundos\n};\n/**\n * Configuración de reintentos\n */ const RETRY_CONFIG = {\n    MAX_ATTEMPTS: 3,\n    BACKOFF_MULTIPLIER: 2,\n    INITIAL_DELAY_MS: 1000\n};\n/**\n * Configuración de seguridad\n */ const SECURITY_CONFIG = {\n    ENABLE_STRICT_VALIDATION: process.env.STRICT_PLAN_VALIDATION === 'true',\n    REQUIRE_PAYMENT_VERIFICATION: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',\n    ENABLE_ACCESS_LOGGING: process.env.ENABLE_ACCESS_LOGGING === 'true',\n    ENABLE_FEATURE_VALIDATION: process.env.ENABLE_FEATURE_VALIDATION === 'true',\n    AUTO_ACTIVATE_PAYMENTS: process.env.AUTO_ACTIVATE_PAYMENTS === 'true',\n    ENABLE_PUBLIC_SIGNUP: process.env.ENABLE_PUBLIC_SIGNUP === 'true'\n};\n// ============================================================================\n// CONSTANTES DE MENSAJES Y TEXTOS\n// ============================================================================\n/**\n * Mensajes de error comunes\n */ const ERROR_MESSAGES = {\n    UNAUTHORIZED: 'No autorizado',\n    FORBIDDEN: 'Acceso denegado',\n    NOT_FOUND: 'Recurso no encontrado',\n    INTERNAL_ERROR: 'Error interno del servidor',\n    INVALID_DATA: 'Datos inválidos',\n    USER_NOT_FOUND: 'Usuario no encontrado',\n    PROFILE_NOT_FOUND: 'Perfil de usuario no encontrado',\n    PAYMENT_REQUIRED: 'Pago requerido',\n    LIMIT_EXCEEDED: 'Límite excedido',\n    FILE_TOO_LARGE: 'El archivo es demasiado grande',\n    INVALID_FILE_TYPE: 'Tipo de archivo no válido',\n    UPLOAD_FAILED: 'Error al subir el archivo',\n    PROCESSING_ERROR: 'Error al procesar la solicitud',\n    NETWORK_ERROR: 'Error de conexión',\n    TIMEOUT_ERROR: 'Tiempo de espera agotado'\n};\n/**\n * Mensajes de éxito\n */ const SUCCESS_MESSAGES = {\n    UPLOAD_SUCCESS: 'Archivo subido correctamente',\n    SAVE_SUCCESS: 'Guardado correctamente',\n    UPDATE_SUCCESS: 'Actualizado correctamente',\n    DELETE_SUCCESS: 'Eliminado correctamente',\n    PAYMENT_SUCCESS: 'Pago procesado correctamente',\n    REGISTRATION_SUCCESS: 'Registro completado',\n    LOGIN_SUCCESS: 'Sesión iniciada',\n    LOGOUT_SUCCESS: 'Sesión cerrada',\n    PASSWORD_RESET_SUCCESS: 'Contraseña restablecida',\n    EMAIL_SENT: 'Email enviado correctamente'\n};\n/**\n * Mensajes de validación\n */ const VALIDATION_MESSAGES = {\n    REQUIRED_FIELD: 'Este campo es obligatorio',\n    INVALID_EMAIL: 'Email no válido',\n    PASSWORD_TOO_SHORT: \"La contrase\\xf1a debe tener al menos \".concat(TEXT_LIMITS.MIN_PASSWORD_LENGTH, \" caracteres\"),\n    PASSWORD_TOO_LONG: \"La contrase\\xf1a no puede tener m\\xe1s de \".concat(TEXT_LIMITS.MAX_PASSWORD_LENGTH, \" caracteres\"),\n    PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',\n    INVALID_FILE_SIZE: \"El archivo no puede superar \".concat(FILE_LIMITS.MAX_SIZE_MB, \"MB\"),\n    INVALID_FILE_FORMAT: 'Formato de archivo no válido',\n    TEXT_TOO_SHORT: \"El texto debe tener al menos \".concat(TEXT_LIMITS.MIN_CONTENT_LENGTH, \" caracteres\"),\n    TEXT_TOO_LONG: \"El texto no puede superar \".concat(TEXT_LIMITS.MAX_CONTENT_LENGTH, \" caracteres\")\n};\n// ============================================================================\n// CONSTANTES DE ESTADO Y TIPOS\n// ============================================================================\n/**\n * Estados de procesamiento\n */ const PROCESSING_STATES = {\n    IDLE: 'idle',\n    LOADING: 'loading',\n    PROCESSING: 'processing',\n    SUCCESS: 'success',\n    ERROR: 'error',\n    CANCELLED: 'cancelled'\n};\n/**\n * Estados de pago\n */ const PAYMENT_STATES = {\n    PENDING: 'pending',\n    PROCESSING: 'processing',\n    COMPLETED: 'completed',\n    FAILED: 'failed',\n    CANCELLED: 'cancelled',\n    REFUNDED: 'refunded'\n};\n/**\n * Tipos de notificación\n */ const NOTIFICATION_TYPES = {\n    INFO: 'info',\n    SUCCESS: 'success',\n    WARNING: 'warning',\n    ERROR: 'error'\n};\n/**\n * Niveles de severidad\n */ const SEVERITY_LEVELS = {\n    LOW: 'low',\n    MEDIUM: 'medium',\n    HIGH: 'high',\n    CRITICAL: 'critical'\n};\n// ============================================================================\n// CONSTANTES DE CONFIGURACIÓN DE ENTORNO\n// ============================================================================\n/**\n * Variables de entorno requeridas\n */ const REQUIRED_ENV_VARS = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'SUPABASE_SERVICE_ROLE_KEY',\n    'STRIPE_SECRET_KEY',\n    'STRIPE_WEBHOOK_SECRET',\n    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'\n];\n/**\n * Configuración de automatización\n */ const AUTOMATION_CONFIG = {\n    INVITATION_EXPIRY_HOURS: parseInt(process.env.INVITATION_EXPIRY_HOURS || '24'),\n    DEFAULT_TRIAL_DAYS: 5,\n    DEFAULT_GRACE_PERIOD_DAYS: 3\n};\n// ============================================================================\n// CONSTANTES DE BUSINESS LOGIC\n// ============================================================================\n/**\n * Costos y precios (en centavos)\n */ const PRICING = {\n    ADDITIONAL_TOKENS_PRICE: 1000,\n    ADDITIONAL_TOKENS_AMOUNT: 1000000,\n    FREE_PLAN_PRICE: 0,\n    BASIC_PLAN_PRICE: 999,\n    PRO_PLAN_PRICE: 1999 // €19.99\n};\n/**\n * Límites de planes gratuitos\n */ const FREE_PLAN_LIMITS = {\n    DOCUMENTS: 1,\n    MIND_MAPS_TRIAL: 2,\n    TESTS_TRIAL: 10,\n    FLASHCARDS_TRIAL: 10,\n    TOKENS_TRIAL: 50000,\n    TRIAL_DAYS: 5\n};\n/**\n * Factores de riesgo de seguridad\n */ const SECURITY_RISK_SCORES = {\n    MISSING_USER_AGENT: 30,\n    BOT_USER_AGENT: 20,\n    EXTERNAL_REFERER: 10,\n    SUSPICIOUS_PATTERN: 25,\n    HIGH_FREQUENCY: 40\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/constants.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config */ \"(app-pages-browser)/./src/config/index.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return (plan === null || plan === void 0 ? void 0 : plan.planConfig) || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        stripePriceId: 'price_tokens_additional'\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: \"\".concat(\"http://localhost:3001\", \"/thank-you\"),\n    cancel: \"\".concat(\"http://localhost:3001\", \"/upgrade-plan\"),\n    webhook: \"\".concat(\"http://localhost:3001\", \"/api/stripe/webhook\")\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/stripe/plans.ts\n"));

/***/ })

});