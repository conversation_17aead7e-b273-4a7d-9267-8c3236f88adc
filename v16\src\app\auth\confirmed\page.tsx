'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { FiLoader, FiCheckCircle, FiAlertTriangle } from 'react-icons/fi';

type ConfirmationStatus = 'loading' | 'success' | 'error' | 'redirecting';

export default function ConfirmedPage() {
  const router = useRouter();
  const supabase = createClient();
  const [status, setStatus] = useState<ConfirmationStatus>('loading');
  const [message, setMessage] = useState('Verificando tu confirmación de email...');

  useEffect(() => {
    const handleEmailConfirmation = async () => {
      try {
        // Escuchar cambios en el estado de autenticación
        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
          console.log('Auth event:', event, 'Session:', session);

          if ((event === 'SIGNED_IN' || event === 'USER_UPDATED' || event === 'INITIAL_SESSION') && session){
            setMessage('Email confirmado exitosamente. Verificando tu perfil...');
            
            try {
              // Obtener el perfil del usuario para determinar el siguiente paso
              const { data: profile, error: profileError } = await supabase
                .from('user_profiles')
                .select('*')
                .eq('user_id', session.user.id)
                .single();

              if (profileError) {
                console.error('Error obteniendo perfil:', profileError);
                setStatus('error');
                setMessage('Error al verificar tu perfil. Por favor, contacta con soporte.');
                return;
              }

              if (!profile) {
                setStatus('error');
                setMessage('No se encontró tu perfil. Por favor, contacta con soporte.');
                return;
              }

              // Determinar redirección basada en el estado del perfil
              if (profile.payment_verified === false && profile.subscription_plan !== 'free') {
                // Usuario de pago pendiente de verificación
                setMessage('Redirigiendo a completar tu pago...');
                setStatus('redirecting');
                setTimeout(() => {
                  router.push('/payment?plan=' + profile.subscription_plan);
                }, 2000);
              } else if (profile.payment_verified === true || profile.subscription_plan === 'free') {
                // Usuario completamente activado
                setMessage('¡Cuenta activada! Redirigiendo a tu dashboard...');
                setStatus('success');
                setTimeout(() => {
                  router.push('/app');
                }, 2000);
              } else {
                // Estado inesperado
                setStatus('error');
                setMessage('Estado de cuenta inesperado. Por favor, contacta con soporte.');
              }

            } catch (error) {
              console.error('Error procesando confirmación:', error);
              setStatus('error');
              setMessage('Error al procesar la confirmación. Por favor, intenta de nuevo.');
            }
          } else if (event === 'SIGNED_OUT') {
            setStatus('error');
            setMessage('Error en la confirmación. Por favor, intenta de nuevo.');
          }
        });

        // Limpiar el listener cuando el componente se desmonte
        return () => {
          subscription.unsubscribe();
        };

      } catch (error) {
        console.error('Error en handleEmailConfirmation:', error);
        setStatus('error');
        setMessage('Error al procesar la confirmación. Por favor, intenta de nuevo.');
      }
    };

    handleEmailConfirmation();
  }, [supabase, router]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <FiLoader className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin" />;
      case 'success':
      case 'redirecting':
        return <FiCheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />;
      case 'error':
        return <FiAlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-4" />;
      default:
        return <FiLoader className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin" />;
    }
  };

  const getTitle = () => {
    switch (status) {
      case 'loading':
        return 'Confirmando tu email...';
      case 'success':
        return '¡Email confirmado!';
      case 'redirecting':
        return '¡Confirmación exitosa!';
      case 'error':
        return 'Error en la confirmación';
      default:
        return 'Procesando...';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
      <div className="bg-white shadow-lg rounded-lg p-8 max-w-md w-full text-center">
        {getIcon()}
        <h1 className="text-2xl font-semibold text-gray-800 mb-4">
          {getTitle()}
        </h1>
        <p className="text-gray-600 mb-6">
          {message}
        </p>
        
        {status === 'error' && (
          <div className="space-y-4">
            <button
              onClick={() => router.push('/auth/login')}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Ir a Iniciar Sesión
            </button>
            <button
              onClick={() => router.push('/')}
              className="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
            >
              Volver al Inicio
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
