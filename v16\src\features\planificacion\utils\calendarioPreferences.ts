/**
 * Utilidades para manejar las preferencias del calendario en localStorage
 * Solo para preferencias de UI, NO para datos críticos
 */

import { useState, useEffect, useCallback } from 'react';

export interface CalendarioPreferences {
  mesActual?: number;
  yearActual?: number;
  fechaSeleccionada?: string; // ISO string
  calendarioExpandido?: boolean;
  primerDiaSemana?: 0 | 1; // 0 = Domingo, 1 = Lunes
  vistaTamaño?: 'compacto' | 'normal' | 'grande';
}

const STORAGE_KEY = 'oposiciones-ia-calendario-prefs';

/**
 * Obtiene las preferencias del calendario desde localStorage
 */
export function obtenerPreferenciasCalendario(): CalendarioPreferences {
  if (typeof window === 'undefined') {
    return getDefaultPreferences();
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return getDefaultPreferences();
    }

    const parsed = JSON.parse(stored);
    
    // Validar y limpiar datos
    return {
      mesActual: typeof parsed.mesActual === 'number' && parsed.mesActual >= 0 && parsed.mesActual <= 11 
        ? parsed.mesActual 
        : undefined,
      yearActual: typeof parsed.yearActual === 'number' && parsed.yearActual >= 2020 && parsed.yearActual <= 2030
        ? parsed.yearActual 
        : undefined,
      fechaSeleccionada: typeof parsed.fechaSeleccionada === 'string' && isValidDateString(parsed.fechaSeleccionada)
        ? parsed.fechaSeleccionada 
        : undefined,
      calendarioExpandido: typeof parsed.calendarioExpandido === 'boolean' 
        ? parsed.calendarioExpandido 
        : true,
      primerDiaSemana: (parsed.primerDiaSemana === 0 || parsed.primerDiaSemana === 1) 
        ? parsed.primerDiaSemana 
        : 1, // Por defecto Lunes para España
      vistaTamaño: ['compacto', 'normal', 'grande'].includes(parsed.vistaTamaño) 
        ? parsed.vistaTamaño 
        : 'normal'
    };
  } catch (error) {
    console.warn('Error al leer preferencias del calendario:', error);
    return getDefaultPreferences();
  }
}

/**
 * Guarda las preferencias del calendario en localStorage
 */
export function guardarPreferenciasCalendario(preferences: Partial<CalendarioPreferences>): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    const current = obtenerPreferenciasCalendario();
    const updated = { ...current, ...preferences };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
  } catch (error) {
    console.warn('Error al guardar preferencias del calendario:', error);
  }
}

/**
 * Actualiza una preferencia específica
 */
export function actualizarPreferencia<K extends keyof CalendarioPreferences>(
  key: K, 
  value: CalendarioPreferences[K]
): void {
  guardarPreferenciasCalendario({ [key]: value });
}

/**
 * Limpia las preferencias del calendario
 */
export function limpiarPreferenciasCalendario(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.warn('Error al limpiar preferencias del calendario:', error);
  }
}

/**
 * Obtiene las preferencias por defecto
 */
function getDefaultPreferences(): CalendarioPreferences {
  const hoy = new Date();
  return {
    mesActual: hoy.getMonth(),
    yearActual: hoy.getFullYear(),
    fechaSeleccionada: undefined,
    calendarioExpandido: true,
    primerDiaSemana: 1, // Lunes para España
    vistaTamaño: 'normal'
  };
}

/**
 * Valida si una cadena es una fecha ISO válida
 */
function isValidDateString(dateString: string): boolean {
  if (!dateString || typeof dateString !== 'string') {
    return false;
  }

  const date = new Date(dateString);
  return !isNaN(date.getTime()) && dateString.includes('T');
}

/**
 * Hook para usar las preferencias del calendario con persistencia
 */
export function useCalendarioPreferences() {
  const [preferences, setPreferencesState] = useState<CalendarioPreferences>(getDefaultPreferences);

  // Cargar preferencias al montar
  useEffect(() => {
    const stored = obtenerPreferenciasCalendario();
    setPreferencesState(stored);
  }, []);

  // Función para actualizar preferencias
  const updatePreferences = useCallback((newPrefs: Partial<CalendarioPreferences>) => {
    setPreferencesState(prev => {
      const updated = { ...prev, ...newPrefs };
      guardarPreferenciasCalendario(newPrefs);
      return updated;
    });
  }, []);

  // Función para actualizar una preferencia específica
  const updatePreference = useCallback(<K extends keyof CalendarioPreferences>(
    key: K, 
    value: CalendarioPreferences[K]
  ) => {
    updatePreferences({ [key]: value });
  }, [updatePreferences]);

  return {
    preferences,
    updatePreferences,
    updatePreference,
    clearPreferences: () => {
      limpiarPreferenciasCalendario();
      setPreferencesState(getDefaultPreferences());
    }
  };
}



/**
 * Migra preferencias antiguas si existen
 */
export function migrarPreferenciasAntiguas(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Buscar claves antiguas que podrían existir
    const oldKeys = [
      'plan-calendario-mes',
      'plan-calendario-year', 
      'plan-calendario-expandido'
    ];

    let hasOldData = false;
    const migratedData: Partial<CalendarioPreferences> = {};

    oldKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value !== null) {
        hasOldData = true;
        
        switch (key) {
          case 'plan-calendario-mes':
            const mes = parseInt(value);
            if (!isNaN(mes) && mes >= 0 && mes <= 11) {
              migratedData.mesActual = mes;
            }
            break;
          case 'plan-calendario-year':
            const year = parseInt(value);
            if (!isNaN(year) && year >= 2020 && year <= 2030) {
              migratedData.yearActual = year;
            }
            break;
          case 'plan-calendario-expandido':
            migratedData.calendarioExpandido = value === 'true';
            break;
        }
        
        // Limpiar clave antigua
        localStorage.removeItem(key);
      }
    });

    // Si había datos antiguos, guardarlos en el nuevo formato
    if (hasOldData) {
      guardarPreferenciasCalendario(migratedData);
      console.log('Preferencias del calendario migradas exitosamente');
    }
  } catch (error) {
    console.warn('Error al migrar preferencias antiguas:', error);
  }
}

/**
 * Obtiene la fecha seleccionada desde las preferencias
 */
export function obtenerFechaSeleccionadaGuardada(): Date | null {
  const prefs = obtenerPreferenciasCalendario();
  if (!prefs.fechaSeleccionada) {
    return null;
  }

  try {
    const fecha = new Date(prefs.fechaSeleccionada);
    return isNaN(fecha.getTime()) ? null : fecha;
  } catch {
    return null;
  }
}

/**
 * Guarda la fecha seleccionada en las preferencias
 */
export function guardarFechaSeleccionada(fecha: Date | null): void {
  actualizarPreferencia('fechaSeleccionada', fecha ? fecha.toISOString() : undefined);
}
