# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://fxnhpxjijinfuxxxplzj.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5MTgwNCwiZXhwIjoyMDYzMDY3ODA0fQ.2dZuBBYg8otnLDhCXhGzrJ69-We578rcUDIpAt33zlg

# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Gemini AI Configuration (legacy - ya no se usa)
#GEMINI_API_KEY=AIzaSyBym-_g5Gv23Dfpe8HQKnqHnJ0mQ_4JIjg

# Email Configuration (Resend)
RESEND_API_KEY=re_BAyF3nde_Df67QTZmWa1eXjZ9dUkW5d8i
NOTIFICATION_EMAIL=<EMAIL>

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51RWD1z07kFn3sIXhdMLMkzfOQdbmqC1sqdK4TUT5Ltr75arGtSam4ROsJf0dmnv8m9ZM1Y5MFcv4pDGaU9OoDC6t00yRyXcgtj
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RWD1z07kFn3sIXhB0Fpdp1titykyAjAQDi7fkkDbWBzLk4jgbmEl93t530dL0ItafvxNnH7wgOBqwPnSwrewYnk00aAQKZ4sw
STRIPE_WEBHOOK_SECRET=whsec_395b64114628d4d602bc5bdb1e179ba9cf613a002b7f894945ab8ae62d2661b1

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_SITE_URL=http://localhost:3001

# Automation Configuration
ENABLE_PUBLIC_SIGNUP=false
REQUIRE_PAYMENT_VERIFICATION=true
INVITATION_EXPIRY_HOURS=24
AUTO_ACTIVATE_PAYMENTS=true

# Security Configuration
ENABLE_FEATURE_VALIDATION=true
ENABLE_ACCESS_LOGGING=true
STRICT_PLAN_VALIDATION=true