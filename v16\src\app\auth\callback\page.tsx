// src/app/auth/callback/page.tsx
// VERSIÓN MODIFICADA CON MANEJO MEJORADO Y FLAG PARA SETUP DE CONTRASEÑA

'use client';

import { useEffect, useState, Suspense, useRef } from 'react'; // Añadir useRef
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/supabaseClient'; // Usar tu cliente configurado

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Procesando autenticación, por favor espera...');
  const [logMessages, setLogMessages] = useState<string[]>([]);
  const timeoutIdRef = useRef<NodeJS.Timeout | null>(null); // Usar ref para el ID del timeout

  // Función para añadir logs al estado y a la consola
  const log = (msg: string, data?: any) => {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${msg}`;
    console.log(logEntry, data || '');
    setLogMessages(prev => [...prev, logEntry]);
  };

  useEffect(() => {
    log('=== [AUTH-CALLBACK-PAGE] AuthCallbackContent useEffect INICIADO ===');

    // Log inicial de la URL para ver qué llega
    log('🌐 [AUTH-CALLBACK-PAGE] URL Completa en el cliente:', window.location.href);
    log('📋 [AUTH-CALLBACK-PAGE] Query Params (searchParams):', Object.fromEntries(searchParams.entries()));
    log('🔍 [AUTH-CALLBACK-PAGE] Verificando si llegamos desde el servidor o directamente...');

    const errorDescription = searchParams.get('error_description');
    const errorCode = searchParams.get('error_code');
    const errorParam = searchParams.get('error');

    if (errorDescription || errorCode || errorParam) {
      let friendlyMessage = decodeURIComponent(errorDescription || errorParam || 'Error desconocido');
      if (errorCode === 'user_already_invited' || (errorDescription && errorDescription.includes('User already invited'))) {
        friendlyMessage = 'Ya se ha enviado una invitación a este correo. Por favor, revisa tu bandeja de entrada (y spam). Si no la encuentras, puedes intentar recuperar tu contraseña desde la página de login.';
      } else if (errorCode === 'token_expired_or_invalid' || (errorDescription && (errorDescription.includes('invalid token') || errorDescription.includes('expired')))) {
        friendlyMessage = 'El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta registrarte o iniciar sesión de nuevo.';
      } else if (friendlyMessage.includes('Código de autenticación no encontrado')) {
        friendlyMessage = 'El enlace de confirmación no es válido. Por favor, verifica que hayas copiado la URL completa del email.';
      } else if (friendlyMessage.includes('No se pudo completar la autenticación')) {
        friendlyMessage = 'No se pudo completar la verificación de tu email. Por favor, intenta usar el enlace del email nuevamente.';
      }
      log('❌ Error explícito detectado en la URL.', { error: friendlyMessage, code: errorCode, error_param: errorParam });
      setStatus('error');
      setMessage(friendlyMessage);
      if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current); // Limpiar timeout si hay error
      return;
    }

    const supabase = createClient();
    log('Cliente Supabase del navegador inicializado.');

    // Verificar si hay código en la URL y dar tiempo a Supabase para procesarlo
    const code = searchParams.get('code');
    if (code) {
      log('🔑 [AUTH-CALLBACK-PAGE] Código encontrado en URL:', code.substring(0, 10) + '...');
      log('⏳ [AUTH-CALLBACK-PAGE] Esperando que Supabase procese automáticamente el código...');

      // Dar tiempo a Supabase para procesar el código automáticamente
      setTimeout(() => {
        supabase.auth.getSession().then(({ data: { session } }) => {
          if (session) {
            log('✅ [AUTH-CALLBACK-PAGE] Sesión encontrada después del procesamiento automático');
            // El listener onAuthStateChange debería detectar esto
          } else {
            log('⚠️ [AUTH-CALLBACK-PAGE] No hay sesión después del procesamiento automático, verificando...');
            // Si no hay sesión, el listener INITIAL_SESSION lo manejará
          }
        });
      }, 1000); // Dar 1 segundo para que Supabase procese
    }

    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      log(`EVENTO onAuthStateChange RECIBIDO: ${event}`, {
          hasSession: !!session,
          userId: session?.user?.id,
          userMetadata: session?.user?.user_metadata
      });

      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current); // Limpiar el timeout si se maneja un evento
        timeoutIdRef.current = null;
      }

      if (event === 'PASSWORD_RECOVERY') {
        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');
        setStatus('success');
        setMessage('Sesión de recuperación lista. Redirigiendo para establecer contraseña...');
        router.push('/auth/reset-password');
      // ===== MODIFICACIÓN START (Lógica para requires_password_setup en SIGNED_IN) =====
      } else if (event === 'SIGNED_IN') {
        log('✅ Evento SIGNED_IN detectado.');
        if (session?.user) {
          // Verificar si el usuario necesita configurar su contraseña
          const userRequiresPasswordSetup = session.user.user_metadata?.requires_password_setup === true;

          if (userRequiresPasswordSetup) {
             log('👤 Usuario necesita establecer contraseña inicial. Redirigiendo a /auth/reset-password...');
             setStatus('success');
             setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');
             // /auth/reset-password manejará la sesión actual y permitirá actualizar la contraseña
             router.push('/auth/reset-password');
          } else {
            log('🚀 Usuario ya tiene contraseña o no requiere setup. Redirigiendo a /app...');
            setStatus('success');
            setMessage('¡Autenticación exitosa! Redirigiendo...');
            router.push('/app');
          }
        } else {
          log('⚠️ Evento SIGNED_IN sin sesión completa. Comportamiento inesperado.');
          setStatus('error');
          setMessage('Error inesperado durante el inicio de sesión. Por favor, inténtalo de nuevo.');
        }
      // ===== MODIFICACIÓN END =====
      } else if (event === 'USER_UPDATED' && session?.user) {
        // Este evento puede ser útil si la confirmación de email ocurre y luego se necesita setear contraseña.
        log('✅ Evento USER_UPDATED con sesión.');
        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en USER_UPDATED) =====
        const userRequiresPasswordSetup = session.user.user_metadata?.requires_password_setup === true;
        if (userRequiresPasswordSetup) {
            log('👤 Usuario actualizado necesita establecer contraseña. Redirigiendo a /auth/reset-password...');
            setStatus('success');
            setMessage('Actualización de cuenta completada. Crea tu contraseña...');
            router.push('/auth/reset-password');
        } else {
            log('🚀 Cuenta actualizada. Redirigiendo a /app...');
            setStatus('success');
            setMessage('Cuenta actualizada. Redirigiendo...');
            router.push('/app');
        }
        // ===== MODIFICACIÓN END =====
      } else if (event === 'INITIAL_SESSION' && !session) {
        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos o timeout.');
        // No hacer nada, esperar al timeout si no llegan más eventos.
      } else if (event === 'INITIAL_SESSION' && session?.user) {
        // Se encontró una sesión inicial, puede ser de una pestaña anterior o un token válido procesado rápido.
        log('✅ Evento INITIAL_SESSION con sesión. Procediendo como SIGNED_IN.');
        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en INITIAL_SESSION) =====
        const userRequiresPasswordSetup = session.user.user_metadata?.requires_password_setup === true;
        if (userRequiresPasswordSetup) {
           router.push('/auth/reset-password');
        } else {
           router.push('/app');
        }
        // ===== MODIFICACIÓN END =====
      }
    });

    log('Listener onAuthStateChange configurado.');

    // Verificación inmediata de la sesión (por si ya está autenticado)
    const checkCurrentSession = async () => {
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      if (currentSession?.user && status === 'loading') {
        log('✅ Sesión ya existente encontrada inmediatamente.');
        const userRequiresPasswordSetup = currentSession.user.user_metadata?.requires_password_setup === true;
        if (userRequiresPasswordSetup) {
          log('👤 Redirigiendo a /auth/reset-password (sesión existente).');
          setStatus('success');
          setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');
          router.push('/auth/reset-password');
        } else {
          log('🚀 Redirigiendo a /app (sesión existente).');
          setStatus('success');
          setMessage('¡Autenticación exitosa! Redirigiendo...');
          router.push('/app');
        }
        if (timeoutIdRef.current) {
          clearTimeout(timeoutIdRef.current);
          timeoutIdRef.current = null;
        }
      }
    };

    // Ejecutar verificación inmediata
    checkCurrentSession();

    // Timeout reducido para casos donde no se recibe el evento esperado
    timeoutIdRef.current = setTimeout(async () => {
      if (status === 'loading') {
        const { data: { session: finalCheckSession }, error: finalCheckError } = await supabase.auth.getSession();
        if (finalCheckSession?.user) {
          log('✅ Sesión encontrada en la verificación final del timeout.');
          const userRequiresPasswordSetup = finalCheckSession.user.user_metadata?.requires_password_setup === true;
          if (userRequiresPasswordSetup) {
            log('👤 Redirigiendo a /auth/reset-password desde timeout.');
            setStatus('success');
            setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');
            router.push('/auth/reset-password');
          } else {
            log('🚀 Redirigiendo a /app desde timeout.');
            setStatus('success');
            setMessage('¡Autenticación exitosa! Redirigiendo...');
            router.push('/app');
          }
        } else {
          log('❌ TIMEOUT (5s): No se recibió un evento de autenticación válido y no hay sesión activa.');
          log('Detalles del error en la verificación final del timeout:', finalCheckError?.message);
          setStatus('error');
          setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Si el problema persiste, intenta registrarte de nuevo o contacta a soporte.');
        }
      }
    }, 5000); // Reducido a 5 segundos

    return () => {
      log('--- AuthCallbackContent useEffect LIMPIEZA ---');
      authListener?.subscription.unsubscribe();
      log('Suscripción a onAuthStateChange eliminada.');
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        log('Temporizador de timeout limpiado.');
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router, searchParams]);

  // ... (resto del componente JSX sin cambios)
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center">
        {status === 'loading' && (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Procesando...</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">¡Éxito!</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Error de Autenticación</h2>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="space-y-2">
              <button
                onClick={() => router.push('/login')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                Ir a Login
              </button>
            </div>
          </>
        )}

        {/* Logs técnicos ocultos para el usuario - solo en consola */}
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  // ... (código Suspense sin cambios)
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Cargando...</h2>
          <p className="text-gray-600">Procesando autenticación...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}