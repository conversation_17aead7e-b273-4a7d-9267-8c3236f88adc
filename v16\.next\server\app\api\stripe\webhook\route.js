/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/webhook/route";
exports.ids = ["app/api/stripe/webhook/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fwebhook%2Froute&page=%2Fapi%2Fstripe%2Fwebhook%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fwebhook%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fwebhook%2Froute&page=%2Fapi%2Fstripe%2Fwebhook%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fwebhook%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_stripe_webhook_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/webhook/route.ts */ \"(rsc)/./src/app/api/stripe/webhook/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/webhook/route\",\n        pathname: \"/api/stripe/webhook\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/webhook/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\stripe\\\\webhook\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_stripe_webhook_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzdHJpcGUlMkZ3ZWJob29rJTJGcm91dGUmcGFnZT0lMkZhcGklMkZzdHJpcGUlMkZ3ZWJob29rJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGc3RyaXBlJTJGd2ViaG9vayUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNuYWF0YSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNPcG9zSSU1Q3YxNiU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDbmFhdGElNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDT3Bvc0klNUN2MTYmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ21EO0FBQ2hJO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFx2MTZcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc3RyaXBlXFxcXHdlYmhvb2tcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3N0cmlwZS93ZWJob29rL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvc3RyaXBlL3dlYmhvb2tcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N0cmlwZS93ZWJob29rL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHN0cmlwZVxcXFx3ZWJob29rXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fwebhook%2Froute&page=%2Fapi%2Fstripe%2Fwebhook%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fwebhook%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/webhook/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/stripe/webhook/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stripe/config */ \"(rsc)/./src/lib/stripe/config.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/stripeWebhookHandlers */ \"(rsc)/./src/lib/services/stripeWebhookHandlers.ts\");\n// src/app/api/stripe/webhook/route.ts\n\n\n\n\nasync function POST(request) {\n    const startTime = Date.now();\n    try {\n        const body = await request.text();\n        const headersList = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.headers)();\n        const signature = headersList.get('stripe-signature');\n        if (!signature) {\n            console.error('❌ No Stripe signature found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No signature found'\n            }, {\n                status: 400\n            });\n        }\n        const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;\n        if (!webhookSecret) {\n            console.error('❌ No webhook secret configured');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Webhook secret not configured'\n            }, {\n                status: 500\n            });\n        }\n        if (!_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.stripe) {\n            console.error('❌ Stripe not initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Stripe not configured'\n            }, {\n                status: 500\n            });\n        }\n        // Verificar el evento de webhook\n        let event;\n        try {\n            event = _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.stripe.webhooks.constructEvent(body, signature, webhookSecret);\n        } catch (err) {\n            console.error('❌ Webhook signature verification failed:', err);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid signature'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🎯 Webhook event received:', event.type, 'ID:', event.id);\n        // Manejar diferentes tipos de eventos con manejadores específicos\n        let result;\n        switch(event.type){\n            case 'checkout.session.completed':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleCheckoutSessionCompleted(event.data.object);\n                break;\n            case 'payment_intent.succeeded':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handlePaymentIntentSucceeded(event.data.object);\n                break;\n            case 'invoice.payment_succeeded':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleInvoicePaymentSucceeded(event.data.object);\n                break;\n            case 'customer.subscription.created':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleSubscriptionCreated(event.data.object);\n                break;\n            case 'customer.subscription.updated':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleSubscriptionUpdated(event.data.object);\n                break;\n            case 'customer.subscription.deleted':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleSubscriptionDeleted(event.data.object);\n                break;\n            default:\n                console.log('⚠️ Unhandled event type:', event.type);\n                result = {\n                    success: true,\n                    message: `Event type ${event.type} not handled`\n                };\n        }\n        // Log del resultado\n        const processingTime = Date.now() - startTime;\n        console.log(`✅ Webhook processed in ${processingTime}ms:`, {\n            eventType: event.type,\n            eventId: event.id,\n            success: result.success,\n            message: result.message\n        });\n        // Responder según el resultado\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                received: true,\n                processed: true,\n                eventType: event.type,\n                message: result.message,\n                data: result.data,\n                processingTime\n            });\n        } else {\n            console.error('❌ Webhook processing failed:', result.error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                received: true,\n                processed: false,\n                eventType: event.type,\n                error: result.message,\n                details: result.error\n            }, {\n                status: 422\n            }); // Unprocessable Entity\n        }\n    } catch (error) {\n        const processingTime = Date.now() - startTime;\n        console.error('❌ Critical webhook error:', {\n            error: error instanceof Error ? error.message : 'Unknown error',\n            stack: error instanceof Error ? error.stack : undefined,\n            processingTime\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Webhook handler failed',\n            details: error instanceof Error ? error.message : 'Unknown error',\n            processingTime\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/webhook/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/constants.ts":
/*!*********************************!*\
  !*** ./src/config/constants.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* binding */ AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* binding */ AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FILE_LIMITS: () => (/* binding */ FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* binding */ FREE_PLAN_LIMITS),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* binding */ NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* binding */ PAYMENT_STATES),\n/* harmony export */   PRICING: () => (/* binding */ PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* binding */ PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* binding */ PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* binding */ RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* binding */ REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* binding */ RETRY_CONFIG),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* binding */ SECURITY_RISK_SCORES),\n/* harmony export */   SEVERITY_LEVELS: () => (/* binding */ SEVERITY_LEVELS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* binding */ TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* binding */ TIMEOUTS),\n/* harmony export */   TOKEN_LIMITS: () => (/* binding */ TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* binding */ VALIDATION_MESSAGES)\n/* harmony export */ });\n// src/config/constants.ts\n// Constantes centralizadas del sistema\n// ============================================================================\n// CONSTANTES DE APLICACIÓN\n// ============================================================================\n/**\n * URLs y rutas de la aplicación\n */ const APP_URLS = {\n    BASE: \"http://localhost:3000\" || 0,\n    SITE: \"http://localhost:3000\" || 0,\n    UPGRADE_PLAN: '/upgrade-plan',\n    THANK_YOU: '/thank-you',\n    LOGIN: '/login',\n    DASHBOARD: '/app',\n    PROFILE: '/profile',\n    WELCOME: '/welcome'\n};\n/**\n * Rutas públicas (no requieren autenticación)\n */ const PUBLIC_ROUTES = [\n    '/',\n    '/login',\n    '/payment',\n    '/thank-you',\n    '/contact',\n    '/privacy',\n    '/terms',\n    '/auth/callback',\n    '/auth/confirmed',\n    '/auth/unauthorized',\n    '/auth/reset-password',\n    '/auth/confirm-reset',\n    '/api/auth/register-free',\n    '/api/auth/pre-register-paid',\n    '/api/stripe/webhook',\n    '/api/stripe/create-checkout-session',\n    '/api/stripe/create-token-checkout',\n    '/api/notify-signup',\n    '/api/user/status',\n    '/api/health',\n    '/api/auth/initiate-password-setup'\n];\n/**\n * Rutas que requieren autenticación básica\n */ const AUTHENTICATED_ROUTES = [\n    '/app',\n    '/dashboard',\n    '/profile',\n    '/welcome',\n    '/upgrade-plan'\n];\n// ============================================================================\n// CONSTANTES DE VALIDACIÓN Y LÍMITES\n// ============================================================================\n/**\n * Límites de archivos y uploads\n */ const FILE_LIMITS = {\n    MAX_SIZE_MB: 5,\n    MAX_SIZE_BYTES: 5 * 1024 * 1024,\n    ALLOWED_TYPES: [\n        'application/pdf'\n    ],\n    ALLOWED_EXTENSIONS: [\n        '.pdf'\n    ]\n};\n/**\n * Límites de texto y contenido\n */ const TEXT_LIMITS = {\n    MIN_PASSWORD_LENGTH: 8,\n    MAX_PASSWORD_LENGTH: 128,\n    MAX_DOCUMENT_TITLE_LENGTH: 200,\n    MAX_DESCRIPTION_LENGTH: 500,\n    MIN_CONTENT_LENGTH: 100,\n    MAX_CONTENT_LENGTH: 50000\n};\n/**\n * Límites de tokens y uso\n */ const TOKEN_LIMITS = {\n    DEFAULT_FREE_LIMIT: 50000,\n    WARNING_THRESHOLD_PERCENTAGE: 80,\n    CRITICAL_THRESHOLD_PERCENTAGE: 90,\n    EXCEEDED_THRESHOLD_PERCENTAGE: 100\n};\n/**\n * Límites de rate limiting\n */ const RATE_LIMITS = {\n    DEFAULT_WINDOW_MINUTES: 60,\n    DEFAULT_MAX_REQUESTS: 100,\n    API_REQUESTS_PER_MINUTE: 60,\n    UPLOAD_REQUESTS_PER_HOUR: 10,\n    AUTH_ATTEMPTS_PER_HOUR: 5\n};\n// ============================================================================\n// CONSTANTES DE TIEMPO Y CONFIGURACIÓN\n// ============================================================================\n/**\n * Timeouts y intervalos\n */ const TIMEOUTS = {\n    SESSION_TIMEOUT_MS: 5 * 60 * 1000,\n    API_TIMEOUT_MS: 30 * 1000,\n    UPLOAD_TIMEOUT_MS: 60 * 1000,\n    RETRY_DELAY_MS: 1000,\n    POLLING_INTERVAL_MS: 2000 // 2 segundos\n};\n/**\n * Configuración de reintentos\n */ const RETRY_CONFIG = {\n    MAX_ATTEMPTS: 3,\n    BACKOFF_MULTIPLIER: 2,\n    INITIAL_DELAY_MS: 1000\n};\n/**\n * Configuración de seguridad\n */ const SECURITY_CONFIG = {\n    ENABLE_STRICT_VALIDATION: process.env.STRICT_PLAN_VALIDATION === 'true',\n    REQUIRE_PAYMENT_VERIFICATION: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',\n    ENABLE_ACCESS_LOGGING: process.env.ENABLE_ACCESS_LOGGING === 'true',\n    ENABLE_FEATURE_VALIDATION: process.env.ENABLE_FEATURE_VALIDATION === 'true',\n    AUTO_ACTIVATE_PAYMENTS: process.env.AUTO_ACTIVATE_PAYMENTS === 'true',\n    ENABLE_PUBLIC_SIGNUP: process.env.ENABLE_PUBLIC_SIGNUP === 'true'\n};\n// ============================================================================\n// CONSTANTES DE MENSAJES Y TEXTOS\n// ============================================================================\n/**\n * Mensajes de error comunes\n */ const ERROR_MESSAGES = {\n    UNAUTHORIZED: 'No autorizado',\n    FORBIDDEN: 'Acceso denegado',\n    NOT_FOUND: 'Recurso no encontrado',\n    INTERNAL_ERROR: 'Error interno del servidor',\n    INVALID_DATA: 'Datos inválidos',\n    USER_NOT_FOUND: 'Usuario no encontrado',\n    PROFILE_NOT_FOUND: 'Perfil de usuario no encontrado',\n    PAYMENT_REQUIRED: 'Pago requerido',\n    LIMIT_EXCEEDED: 'Límite excedido',\n    FILE_TOO_LARGE: 'El archivo es demasiado grande',\n    INVALID_FILE_TYPE: 'Tipo de archivo no válido',\n    UPLOAD_FAILED: 'Error al subir el archivo',\n    PROCESSING_ERROR: 'Error al procesar la solicitud',\n    NETWORK_ERROR: 'Error de conexión',\n    TIMEOUT_ERROR: 'Tiempo de espera agotado'\n};\n/**\n * Mensajes de éxito\n */ const SUCCESS_MESSAGES = {\n    UPLOAD_SUCCESS: 'Archivo subido correctamente',\n    SAVE_SUCCESS: 'Guardado correctamente',\n    UPDATE_SUCCESS: 'Actualizado correctamente',\n    DELETE_SUCCESS: 'Eliminado correctamente',\n    PAYMENT_SUCCESS: 'Pago procesado correctamente',\n    REGISTRATION_SUCCESS: 'Registro completado',\n    LOGIN_SUCCESS: 'Sesión iniciada',\n    LOGOUT_SUCCESS: 'Sesión cerrada',\n    PASSWORD_RESET_SUCCESS: 'Contraseña restablecida',\n    EMAIL_SENT: 'Email enviado correctamente'\n};\n/**\n * Mensajes de validación\n */ const VALIDATION_MESSAGES = {\n    REQUIRED_FIELD: 'Este campo es obligatorio',\n    INVALID_EMAIL: 'Email no válido',\n    PASSWORD_TOO_SHORT: `La contraseña debe tener al menos ${TEXT_LIMITS.MIN_PASSWORD_LENGTH} caracteres`,\n    PASSWORD_TOO_LONG: `La contraseña no puede tener más de ${TEXT_LIMITS.MAX_PASSWORD_LENGTH} caracteres`,\n    PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',\n    INVALID_FILE_SIZE: `El archivo no puede superar ${FILE_LIMITS.MAX_SIZE_MB}MB`,\n    INVALID_FILE_FORMAT: 'Formato de archivo no válido',\n    TEXT_TOO_SHORT: `El texto debe tener al menos ${TEXT_LIMITS.MIN_CONTENT_LENGTH} caracteres`,\n    TEXT_TOO_LONG: `El texto no puede superar ${TEXT_LIMITS.MAX_CONTENT_LENGTH} caracteres`\n};\n// ============================================================================\n// CONSTANTES DE ESTADO Y TIPOS\n// ============================================================================\n/**\n * Estados de procesamiento\n */ const PROCESSING_STATES = {\n    IDLE: 'idle',\n    LOADING: 'loading',\n    PROCESSING: 'processing',\n    SUCCESS: 'success',\n    ERROR: 'error',\n    CANCELLED: 'cancelled'\n};\n/**\n * Estados de pago\n */ const PAYMENT_STATES = {\n    PENDING: 'pending',\n    PROCESSING: 'processing',\n    COMPLETED: 'completed',\n    FAILED: 'failed',\n    CANCELLED: 'cancelled',\n    REFUNDED: 'refunded'\n};\n/**\n * Tipos de notificación\n */ const NOTIFICATION_TYPES = {\n    INFO: 'info',\n    SUCCESS: 'success',\n    WARNING: 'warning',\n    ERROR: 'error'\n};\n/**\n * Niveles de severidad\n */ const SEVERITY_LEVELS = {\n    LOW: 'low',\n    MEDIUM: 'medium',\n    HIGH: 'high',\n    CRITICAL: 'critical'\n};\n// ============================================================================\n// CONSTANTES DE CONFIGURACIÓN DE ENTORNO\n// ============================================================================\n/**\n * Variables de entorno requeridas\n */ const REQUIRED_ENV_VARS = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'SUPABASE_SERVICE_ROLE_KEY',\n    'STRIPE_SECRET_KEY',\n    'STRIPE_WEBHOOK_SECRET',\n    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'\n];\n/**\n * Configuración de automatización\n */ const AUTOMATION_CONFIG = {\n    INVITATION_EXPIRY_HOURS: parseInt(process.env.INVITATION_EXPIRY_HOURS || '24'),\n    DEFAULT_TRIAL_DAYS: 5,\n    DEFAULT_GRACE_PERIOD_DAYS: 3\n};\n// ============================================================================\n// CONSTANTES DE BUSINESS LOGIC\n// ============================================================================\n/**\n * Costos y precios (en centavos)\n */ const PRICING = {\n    ADDITIONAL_TOKENS_PRICE: 1000,\n    ADDITIONAL_TOKENS_AMOUNT: 1000000,\n    FREE_PLAN_PRICE: 0,\n    BASIC_PLAN_PRICE: 999,\n    PRO_PLAN_PRICE: 1999 // €19.99\n};\n/**\n * Límites de planes gratuitos\n */ const FREE_PLAN_LIMITS = {\n    DOCUMENTS: 1,\n    MIND_MAPS_TRIAL: 2,\n    TESTS_TRIAL: 10,\n    FLASHCARDS_TRIAL: 10,\n    TOKENS_TRIAL: 50000,\n    TRIAL_DAYS: 5\n};\n/**\n * Factores de riesgo de seguridad\n */ const SECURITY_RISK_SCORES = {\n    MISSING_USER_AGENT: 30,\n    BOT_USER_AGENT: 20,\n    EXTERNAL_REFERER: 10,\n    SUSPICIOUS_PATTERN: 25,\n    HIGH_FREQUENCY: 40\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/features.ts":
/*!********************************!*\
  !*** ./src/config/features.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* binding */ ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* binding */ ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* binding */ ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   FEATURES_CONFIG: () => (/* binding */ FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* binding */ FEATURE_IDS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* binding */ PLAN_RESTRICTED_ROUTES),\n/* harmony export */   actionToFeature: () => (/* binding */ actionToFeature),\n/* harmony export */   activityToFeature: () => (/* binding */ activityToFeature),\n/* harmony export */   featureRequiresPayment: () => (/* binding */ featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* binding */ getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* binding */ getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* binding */ getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* binding */ getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* binding */ getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* binding */ getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* binding */ getFeaturesForPlan),\n/* harmony export */   isValidFeatureId: () => (/* binding */ isValidFeatureId)\n/* harmony export */ });\n// src/config/features.ts\n// Configuración centralizada de características y funcionalidades\n// ============================================================================\n// CONSTANTES DE FEATURES\n// ============================================================================\n/**\n * Identificadores únicos de características del sistema\n */ const FEATURE_IDS = {\n    DOCUMENT_UPLOAD: 'document_upload',\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_TUTOR_CHAT: 'ai_tutor_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_A1_A2: 'summary_a1_a2'\n};\n/**\n * Acciones que pueden realizarse en el sistema\n */ const ACTION_TYPES = {\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_CHAT: 'ai_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_GENERATION: 'summary_generation'\n};\n/**\n * Configuración de todas las características del sistema\n */ const FEATURES_CONFIG = {\n    [FEATURE_IDS.DOCUMENT_UPLOAD]: {\n        id: FEATURE_IDS.DOCUMENT_UPLOAD,\n        name: 'document_upload',\n        displayName: 'Subida de documentos',\n        description: 'Permite subir y procesar documentos PDF para estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 0,\n        icon: 'FiUpload',\n        route: '/app'\n    },\n    [FEATURE_IDS.TEST_GENERATION]: {\n        id: FEATURE_IDS.TEST_GENERATION,\n        name: 'test_generation',\n        displayName: 'Generación de tests',\n        description: 'Genera tests automáticos basados en el contenido de estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 5000,\n        icon: 'FiFileText',\n        route: '/app/tests'\n    },\n    [FEATURE_IDS.FLASHCARD_GENERATION]: {\n        id: FEATURE_IDS.FLASHCARD_GENERATION,\n        name: 'flashcard_generation',\n        displayName: 'Generación de flashcards',\n        description: 'Crea flashcards inteligentes para memorización efectiva',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 3000,\n        icon: 'FiLayers',\n        route: '/app/flashcards'\n    },\n    [FEATURE_IDS.MIND_MAP_GENERATION]: {\n        id: FEATURE_IDS.MIND_MAP_GENERATION,\n        name: 'mind_map_generation',\n        displayName: 'Generación de mapas mentales',\n        description: 'Genera mapas mentales visuales para mejor comprensión',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 4000,\n        icon: 'FiGitBranch',\n        route: '/app/mindmaps'\n    },\n    [FEATURE_IDS.AI_TUTOR_CHAT]: {\n        id: FEATURE_IDS.AI_TUTOR_CHAT,\n        name: 'ai_tutor_chat',\n        displayName: 'Chat con preparador IA',\n        description: 'Interactúa con un preparador de oposiciones inteligente',\n        category: 'premium',\n        minimumPlans: [\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 2000,\n        icon: 'FiMessageSquare',\n        route: '/app/ai-tutor'\n    },\n    [FEATURE_IDS.STUDY_PLANNING]: {\n        id: FEATURE_IDS.STUDY_PLANNING,\n        name: 'study_planning',\n        displayName: 'Planificación de estudios',\n        description: 'Crea planes de estudio personalizados y estructurados',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 20000,\n        icon: 'FiCalendar',\n        route: '/plan-estudios'\n    },\n    [FEATURE_IDS.SUMMARY_A1_A2]: {\n        id: FEATURE_IDS.SUMMARY_A1_A2,\n        name: 'summary_a1_a2',\n        displayName: 'Resúmenes A1 y A2',\n        description: 'Genera resúmenes especializados para oposiciones A1 y A2',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 6000,\n        icon: 'FiBook',\n        route: '/app/summaries'\n    }\n};\n// ============================================================================\n// MAPEOS Y UTILIDADES\n// ============================================================================\n/**\n * Mapeo de acciones a características\n */ const ACTION_TO_FEATURE_MAP = {\n    [ACTION_TYPES.TEST_GENERATION]: FEATURE_IDS.TEST_GENERATION,\n    [ACTION_TYPES.FLASHCARD_GENERATION]: FEATURE_IDS.FLASHCARD_GENERATION,\n    [ACTION_TYPES.MIND_MAP_GENERATION]: FEATURE_IDS.MIND_MAP_GENERATION,\n    [ACTION_TYPES.AI_CHAT]: FEATURE_IDS.AI_TUTOR_CHAT,\n    [ACTION_TYPES.STUDY_PLANNING]: FEATURE_IDS.STUDY_PLANNING,\n    [ACTION_TYPES.SUMMARY_GENERATION]: FEATURE_IDS.SUMMARY_A1_A2\n};\n/**\n * Mapeo de actividades de tokens a características\n */ const ACTIVITY_TO_FEATURE_MAP = {\n    'test_generation': FEATURE_IDS.TEST_GENERATION,\n    'flashcard_generation': FEATURE_IDS.FLASHCARD_GENERATION,\n    'mind_map_generation': FEATURE_IDS.MIND_MAP_GENERATION,\n    'ai_chat': FEATURE_IDS.AI_TUTOR_CHAT,\n    'study_planning': FEATURE_IDS.STUDY_PLANNING,\n    'summary_generation': FEATURE_IDS.SUMMARY_A1_A2,\n    'document_analysis': FEATURE_IDS.DOCUMENT_UPLOAD\n};\n/**\n * Configuración de rutas restringidas por plan\n */ const PLAN_RESTRICTED_ROUTES = {\n    '/plan-estudios': [\n        'pro'\n    ],\n    '/app/ai-tutor': [\n        'usuario',\n        'pro'\n    ],\n    '/app/summaries': [\n        'pro'\n    ],\n    '/app/advanced-features': [\n        'pro'\n    ]\n};\n// ============================================================================\n// FUNCIONES UTILITARIAS\n// ============================================================================\n/**\n * Obtiene la configuración de una característica\n */ function getFeatureConfig(featureId) {\n    return FEATURES_CONFIG[featureId];\n}\n/**\n * Obtiene el nombre para mostrar de una característica\n */ function getFeatureDisplayName(featureId) {\n    const config = FEATURES_CONFIG[featureId];\n    return config?.displayName || featureId;\n}\n/**\n * Obtiene todas las características de una categoría\n */ function getFeaturesByCategory(category) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.category === category);\n}\n/**\n * Obtiene las características disponibles para un plan\n */ function getFeaturesForPlan(planId) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.minimumPlans.includes(planId));\n}\n/**\n * Verifica si una característica requiere pago\n */ function featureRequiresPayment(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.requiresPayment || false;\n}\n/**\n * Obtiene los tokens requeridos para una característica\n */ function getFeatureTokensRequired(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.tokensRequired || 0;\n}\n/**\n * Convierte una acción a su característica correspondiente\n */ function actionToFeature(action) {\n    return ACTION_TO_FEATURE_MAP[action];\n}\n/**\n * Convierte una actividad a su característica correspondiente\n */ function activityToFeature(activity) {\n    return ACTIVITY_TO_FEATURE_MAP[activity];\n}\n/**\n * Obtiene todas las características como array\n */ function getAllFeatures() {\n    return Object.values(FEATURES_CONFIG);\n}\n/**\n * Obtiene los IDs de todas las características\n */ function getAllFeatureIds() {\n    return Object.keys(FEATURES_CONFIG);\n}\n/**\n * Verifica si un ID de característica es válido\n */ function isValidFeatureId(featureId) {\n    return featureId in FEATURES_CONFIG;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/features.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/index.ts":
/*!*****************************!*\
  !*** ./src/config/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES),\n/* harmony export */   FEATURES_CONFIG: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURE_IDS),\n/* harmony export */   FILE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FREE_PLAN_LIMITS),\n/* harmony export */   LIMITS: () => (/* binding */ LIMITS),\n/* harmony export */   MESSAGES: () => (/* binding */ MESSAGES),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PAYMENT_STATES),\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.PLAN_RESTRICTED_ROUTES),\n/* harmony export */   PRICING: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG),\n/* harmony export */   SECURITY: () => (/* binding */ SECURITY),\n/* harmony export */   SECURITY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS),\n/* harmony export */   TIME_CONFIG: () => (/* binding */ TIME_CONFIG),\n/* harmony export */   TOKEN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES),\n/* harmony export */   actionToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.actionToFeature),\n/* harmony export */   activityToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.activityToFeature),\n/* harmony export */   canPerformAction: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.checkUserFeatureAccess),\n/* harmony export */   featureRequiresPayment: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesForPlan),\n/* harmony export */   getPlanConfiguration: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.isUnlimited),\n/* harmony export */   isValidFeatureId: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.isValidFeatureId)\n/* harmony export */ });\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plans */ \"(rsc)/./src/config/plans.ts\");\n/* harmony import */ var _features__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./features */ \"(rsc)/./src/config/features.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(rsc)/./src/config/constants.ts\");\n/**\n * Configuración centralizada - Re-exports\n * \n * Este archivo centraliza todas las configuraciones del sistema,\n * proporcionando un punto único de importación para cualquier configuración.\n * \n * Uso:\n * import { PLAN_CONFIGURATIONS, FEATURES_CONFIG, ERROR_MESSAGES } from '@/config';\n */ // ============================================================================\n// CONFIGURACIONES DE PLANES\n// ============================================================================\n\n// ============================================================================\n// CONFIGURACIONES DE FEATURES\n// ============================================================================\n\n// ============================================================================\n// CONSTANTES DEL SISTEMA\n// ============================================================================\n\n// ============================================================================\n// RE-EXPORTS COMBINADOS PARA CONVENIENCIA\n// ============================================================================\n// Importar constantes para uso en re-exports combinados\n\n/**\n * Todas las configuraciones de límites en un solo objeto\n */ const LIMITS = {\n    FILE: _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS,\n    TEXT: _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS,\n    TOKEN: _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS,\n    RATE: _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS\n};\n/**\n * Todas las configuraciones de tiempo en un solo objeto\n */ const TIME_CONFIG = {\n    TIMEOUTS: _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS,\n    RETRY: _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG\n};\n/**\n * Todos los mensajes del sistema en un solo objeto\n */ const MESSAGES = {\n    ERROR: _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES,\n    SUCCESS: _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES,\n    VALIDATION: _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES\n};\n/**\n * Todas las configuraciones de seguridad en un solo objeto\n */ const SECURITY = {\n    CONFIG: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG,\n    RISK_SCORES: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/plans.ts":
/*!*****************************!*\
  !*** ./src/config/plans.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/config/plans.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailAnalytics.ts":
/*!**************************************************!*\
  !*** ./src/lib/services/email/emailAnalytics.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailAnalytics: () => (/* binding */ EmailAnalytics)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n// src/lib/services/email/emailAnalytics.ts\n// Análisis y estadísticas de notificaciones por email\n\nclass EmailAnalytics {\n    /**\n   * Obtener estadísticas de notificaciones por tipo\n   */ static async getNotificationStats(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            const byType = (notifications || []).reduce((acc, notif)=>{\n                acc[notif.type] = (acc[notif.type] || 0) + 1;\n                return acc;\n            }, {});\n            const byStatus = (notifications || []).reduce((acc, notif)=>{\n                acc[notif.status] = (acc[notif.status] || 0) + 1;\n                return acc;\n            }, {});\n            const recentNotifications = (notifications || []).sort((a, b)=>new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime()).slice(0, 10);\n            return {\n                byType,\n                byStatus,\n                total: notifications?.length || 0,\n                recentNotifications\n            };\n        } catch (error) {\n            console.error('Error obteniendo estadísticas de notificaciones:', error);\n            return {\n                byType: {},\n                byStatus: {},\n                total: 0,\n                recentNotifications: []\n            };\n        }\n    }\n    /**\n   * Obtener estadísticas de fallos y errores\n   */ static async getFailureStats(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('status', 'failed');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: failures, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Obtener total de notificaciones para calcular tasa de fallo\n            let totalQuery = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*', {\n                count: 'exact',\n                head: true\n            });\n            if (startDate) {\n                totalQuery = totalQuery.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                totalQuery = totalQuery.lte('sent_at', endDate);\n            }\n            const { count: totalCount } = await totalQuery;\n            // Agrupar errores por tipo\n            const errorsByType = (failures || []).reduce((acc, failure)=>{\n                const errorMessage = failure.metadata?.error_message || 'Unknown error';\n                const errorType = this.categorizeError(errorMessage);\n                acc[errorType] = (acc[errorType] || 0) + 1;\n                return acc;\n            }, {});\n            const totalFailures = failures?.length || 0;\n            const failureRate = totalCount && totalCount > 0 ? totalFailures / totalCount * 100 : 0;\n            return {\n                totalFailures,\n                failureRate: Math.round(failureRate * 100) / 100,\n                errorsByType,\n                recentFailures: (failures || []).sort((a, b)=>new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime()).slice(0, 10).map((failure)=>({\n                        id: failure.id,\n                        type: failure.type,\n                        recipient: failure.recipient_email,\n                        error: failure.metadata?.error_message || 'Unknown error',\n                        failedAt: failure.metadata?.failed_at || failure.sent_at\n                    }))\n            };\n        } catch (error) {\n            console.error('Error obteniendo estadísticas de fallos:', error);\n            return {\n                totalFailures: 0,\n                failureRate: 0,\n                errorsByType: {},\n                recentFailures: []\n            };\n        }\n    }\n    /**\n   * Categorizar errores para estadísticas\n   */ static categorizeError(errorMessage) {\n        const message = errorMessage.toLowerCase();\n        if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {\n            return 'Network Error';\n        }\n        if (message.includes('invalid') || message.includes('malformed') || message.includes('email')) {\n            return 'Invalid Email';\n        }\n        if (message.includes('rate limit') || message.includes('quota') || message.includes('limit')) {\n            return 'Rate Limit';\n        }\n        if (message.includes('auth') || message.includes('key') || message.includes('permission')) {\n            return 'Authentication Error';\n        }\n        if (message.includes('bounce') || message.includes('reject')) {\n            return 'Email Bounced';\n        }\n        return 'Other Error';\n    }\n    /**\n   * Obtener métricas de rendimiento por período\n   */ static async getPerformanceMetrics(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            const totalSent = notifications?.length || 0;\n            const successful = notifications?.filter((n)=>n.status === 'sent').length || 0;\n            const successRate = totalSent > 0 ? successful / totalSent * 100 : 0;\n            // Agrupar por hora del día para encontrar picos\n            const peakHours = (notifications || []).reduce((acc, notif)=>{\n                const hour = new Date(notif.sent_at).getHours();\n                acc[hour] = (acc[hour] || 0) + 1;\n                return acc;\n            }, {});\n            // Agrupar por día\n            const dailyVolume = (notifications || []).reduce((acc, notif)=>{\n                const day = notif.sent_at.split('T')[0];\n                acc[day] = (acc[day] || 0) + 1;\n                return acc;\n            }, {});\n            return {\n                totalSent,\n                successRate: Math.round(successRate * 100) / 100,\n                avgResponseTime: 0,\n                peakHours,\n                dailyVolume\n            };\n        } catch (error) {\n            console.error('Error obteniendo métricas de rendimiento:', error);\n            return {\n                totalSent: 0,\n                successRate: 0,\n                avgResponseTime: 0,\n                peakHours: {},\n                dailyVolume: {}\n            };\n        }\n    }\n    /**\n   * Obtener top usuarios por volumen de notificaciones\n   */ static async getTopUsersByVolume(limit = 10, startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('user_id, recipient_email, sent_at').not('user_id', 'is', null);\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Agrupar por usuario\n            const userStats = (notifications || []).reduce((acc, notif)=>{\n                const userId = notif.user_id;\n                if (!acc[userId]) {\n                    acc[userId] = {\n                        userId,\n                        email: notif.recipient_email,\n                        count: 0,\n                        lastNotification: notif.sent_at\n                    };\n                }\n                acc[userId].count++;\n                if (new Date(notif.sent_at) > new Date(acc[userId].lastNotification)) {\n                    acc[userId].lastNotification = notif.sent_at;\n                }\n                return acc;\n            }, {});\n            // Convertir a array y ordenar por count\n            return Object.values(userStats).sort((a, b)=>b.count - a.count).slice(0, limit);\n        } catch (error) {\n            console.error('Error obteniendo top usuarios:', error);\n            return [];\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailAnalytics.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailLogger.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/email/emailLogger.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailLogger: () => (/* binding */ EmailLogger)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n// src/lib/services/email/emailLogger.ts\n// Logging y tracking de notificaciones en base de datos\n\nclass EmailLogger {\n    /**\n   * Registrar notificación en base de datos para tracking\n   */ static async logEmailNotification(notification, status = 'sent') {\n        try {\n            const insertData = {\n                recipient_email: notification.to,\n                subject: notification.subject,\n                type: notification.type,\n                sent_at: new Date().toISOString(),\n                status: status\n            };\n            // Agregar user_id si está disponible\n            if (notification.userId) {\n                insertData.user_id = notification.userId;\n            }\n            // Agregar metadata si está disponible\n            if (notification.metadata) {\n                insertData.metadata = notification.metadata;\n            }\n            const { data, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').insert(insertData).select('id').single();\n            if (error) {\n                throw error;\n            }\n            console.log('📝 Email notification logged:', {\n                id: data.id,\n                type: notification.type,\n                recipient: notification.to,\n                status: status,\n                userId: notification.userId || 'N/A'\n            });\n            return data.id;\n        } catch (error) {\n            console.error('Error logging email notification:', error);\n            // No lanzar error, es solo para tracking\n            return null;\n        }\n    }\n    /**\n   * Actualizar estado de una notificación existente\n   */ static async updateEmailNotificationStatus(notificationId, status, errorMessage) {\n        try {\n            const updateData = {\n                status: status,\n                updated_at: new Date().toISOString()\n            };\n            // Si es un fallo, agregar el mensaje de error a metadata\n            if (status === 'failed' && errorMessage) {\n                updateData.metadata = {\n                    error_message: errorMessage,\n                    failed_at: new Date().toISOString()\n                };\n            }\n            // Si es exitoso, marcar como entregado\n            if (status === 'sent') {\n                updateData.delivered_at = new Date().toISOString();\n            }\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').update(updateData).eq('id', notificationId);\n            if (error) {\n                throw error;\n            }\n            console.log('📝 Email notification status updated:', {\n                id: notificationId,\n                status: status,\n                error: errorMessage || 'N/A'\n            });\n        } catch (error) {\n            console.error('Error updating email notification status:', error);\n        // No lanzar error, es solo para tracking\n        }\n    }\n    /**\n   * Obtener historial de notificaciones de un usuario\n   */ static async getUserNotifications(userId, limit = 50, type) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('user_id', userId).order('sent_at', {\n                ascending: false\n            });\n            if (type) {\n                query = query.eq('type', type);\n            }\n            if (limit) {\n                query = query.limit(limit);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Obtener total de notificaciones\n            let countQuery = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*', {\n                count: 'exact',\n                head: true\n            }).eq('user_id', userId);\n            if (type) {\n                countQuery = countQuery.eq('type', type);\n            }\n            const { count } = await countQuery;\n            return {\n                notifications: notifications || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error('Error obteniendo notificaciones del usuario:', error);\n            return {\n                notifications: [],\n                total: 0\n            };\n        }\n    }\n    /**\n   * Obtener notificaciones fallidas para reintentos\n   */ static async getFailedNotifications(maxAge = 24, limit = 10 // Máximo 10 por consulta\n    ) {\n        try {\n            const cutoffDate = new Date(Date.now() - maxAge * 60 * 60 * 1000).toISOString();\n            const { data: failedNotifications, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('status', 'failed').gte('sent_at', cutoffDate).limit(limit);\n            if (error) {\n                throw error;\n            }\n            return failedNotifications || [];\n        } catch (error) {\n            console.error('Error obteniendo notificaciones fallidas:', error);\n            return [];\n        }\n    }\n    /**\n   * Marcar notificación como reintentada\n   */ static async markAsRetried(originalNotificationId, success, errorMessage) {\n        try {\n            const status = success ? 'retried_successfully' : 'failed';\n            const message = success ? 'Successfully retried' : errorMessage || 'Retry failed';\n            await this.updateEmailNotificationStatus(originalNotificationId, status, message);\n        } catch (error) {\n            console.error('Error marcando notificación como reintentada:', error);\n        }\n    }\n    /**\n   * Limpiar notificaciones antiguas (para mantenimiento)\n   */ static async cleanupOldNotifications(daysToKeep = 90) {\n        try {\n            const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();\n            console.log(`🧹 Limpiando notificaciones anteriores a: ${cutoffDate}`);\n            const { data, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').delete().lt('sent_at', cutoffDate).select('id');\n            if (error) {\n                throw error;\n            }\n            const deletedCount = data?.length || 0;\n            console.log(`✅ Limpieza completada: ${deletedCount} notificaciones eliminadas`);\n            return {\n                deleted: deletedCount\n            };\n        } catch (error) {\n            console.error('Error en limpieza de notificaciones:', error);\n            return {\n                deleted: 0,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailLogger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailNotificationService.ts":
/*!************************************************************!*\
  !*** ./src/lib/services/email/emailNotificationService.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailNotificationService: () => (/* binding */ EmailNotificationService)\n/* harmony export */ });\n/* harmony import */ var _emailTemplates__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./emailTemplates */ \"(rsc)/./src/lib/services/email/emailTemplates.ts\");\n/* harmony import */ var _emailSender__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./emailSender */ \"(rsc)/./src/lib/services/email/emailSender.ts\");\n/* harmony import */ var _emailLogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./emailLogger */ \"(rsc)/./src/lib/services/email/emailLogger.ts\");\n/* harmony import */ var _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./emailAnalytics */ \"(rsc)/./src/lib/services/email/emailAnalytics.ts\");\n// src/lib/services/email/emailNotificationService.ts\n// Servicio principal de notificaciones por email (API pública)\n\n\n\n\nclass EmailNotificationService {\n    /**\n   * Enviar notificación de cancelación de suscripción\n   */ static async sendSubscriptionCancelledNotification(userEmail, userName, planName, gracePeriodEnd, userId) {\n        try {\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateSubscriptionCancelledEmail(userName, planName, gracePeriodEnd);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'subscription_cancelled',\n                userId,\n                metadata: {\n                    planName,\n                    gracePeriodEnd,\n                    userName,\n                    daysRemaining: Math.ceil((new Date(gracePeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando notificación de cancelación:', error);\n            return false;\n        }\n    }\n    /**\n   * Enviar recordatorio de que el período de gracia está por terminar\n   */ static async sendGracePeriodEndingNotification(userEmail, userName, planName, gracePeriodEnd, userId) {\n        try {\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateGracePeriodEndingEmail(userName, planName, gracePeriodEnd);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'grace_period_ending',\n                userId,\n                metadata: {\n                    planName,\n                    gracePeriodEnd,\n                    userName,\n                    hoursRemaining: Math.ceil((new Date(gracePeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60))\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando recordatorio de período de gracia:', error);\n            return false;\n        }\n    }\n    /**\n   * Enviar notificación genérica\n   */ static async sendGenericNotification(userEmail, userName, title, message, type = 'other', userId, ctaText, ctaUrl) {\n        try {\n            // Generar contenido del email usando template genérico\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateGenericEmail(userName, title, message, ctaText, ctaUrl);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type,\n                userId,\n                metadata: {\n                    userName,\n                    title,\n                    message,\n                    ctaText,\n                    ctaUrl\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando notificación genérica:', error);\n            return false;\n        }\n    }\n    /**\n   * Obtener historial de notificaciones de un usuario\n   */ static async getUserNotifications(userId, limit = 50, type) {\n        return await _emailLogger__WEBPACK_IMPORTED_MODULE_2__.EmailLogger.getUserNotifications(userId, limit, type);\n    }\n    /**\n   * Obtener estadísticas de notificaciones por tipo\n   */ static async getNotificationStats(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getNotificationStats(startDate, endDate);\n    }\n    /**\n   * Obtener estadísticas de fallos y errores\n   */ static async getFailureStats(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getFailureStats(startDate, endDate);\n    }\n    /**\n   * Reenviar notificaciones fallidas\n   */ static async retryFailedNotifications(maxAge = 24, limit = 10) {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.retryFailedNotifications(maxAge, limit);\n    }\n    /**\n   * Obtener métricas de rendimiento\n   */ static async getPerformanceMetrics(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getPerformanceMetrics(startDate, endDate);\n    }\n    /**\n   * Obtener top usuarios por volumen de notificaciones\n   */ static async getTopUsersByVolume(limit = 10, startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getTopUsersByVolume(limit, startDate, endDate);\n    }\n    /**\n   * Enviar email de prueba\n   */ static async sendTestEmail(to, providerConfig) {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendTestEmail(to, providerConfig);\n    }\n    /**\n   * Validar configuración del proveedor de email\n   */ static async validateEmailProvider() {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.validateEmailProvider();\n    }\n    /**\n   * Limpiar notificaciones antiguas\n   */ static async cleanupOldNotifications(daysToKeep = 90) {\n        return await _emailLogger__WEBPACK_IMPORTED_MODULE_2__.EmailLogger.cleanupOldNotifications(daysToKeep);\n    }\n    /**\n   * Obtener resumen del sistema de notificaciones\n   */ static async getSystemSummary() {\n        try {\n            const [providerStatus, recentStats, failureStats, performanceMetrics] = await Promise.all([\n                this.validateEmailProvider(),\n                this.getNotificationStats(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), new Date().toISOString()),\n                this.getFailureStats(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), new Date().toISOString()),\n                this.getPerformanceMetrics(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), new Date().toISOString())\n            ]);\n            return {\n                providerStatus,\n                recentStats,\n                failureStats,\n                performanceMetrics\n            };\n        } catch (error) {\n            console.error('Error obteniendo resumen del sistema:', error);\n            throw error;\n        }\n    }\n    /**\n   * Enviar email de confirmación para upgrade de plan\n   * SEGURIDAD: Este email se envía cuando se detecta un pago para un email existente\n   */ static async sendPlanUpgradeConfirmationEmail(userEmail, userName, newPlanId, confirmationToken) {\n        try {\n            console.log(`📧 [SECURITY] Enviando email de confirmación de upgrade a: ${userEmail}`);\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generatePlanUpgradeConfirmation(userName, newPlanId, confirmationToken);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'plan_upgrade_confirmation',\n                metadata: {\n                    newPlanId,\n                    confirmationToken,\n                    userName,\n                    securityAction: true,\n                    sentAt: new Date().toISOString()\n                }\n            };\n            // Enviar email\n            const result = await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n            if (result) {\n                console.log(`✅ [SECURITY] Email de confirmación enviado exitosamente a: ${userEmail}`);\n            } else {\n                console.error(`❌ [SECURITY] Error enviando email de confirmación a: ${userEmail}`);\n            }\n            return result;\n        } catch (error) {\n            console.error('❌ [SECURITY] Error enviando email de confirmación de upgrade:', error);\n            return false;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL2VtYWlsL2VtYWlsTm90aWZpY2F0aW9uU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLHFEQUFxRDtBQUNyRCwrREFBK0Q7QUFHYjtBQUNOO0FBQ0E7QUFDTTtBQUUzQyxNQUFNSTtJQUVYOztHQUVDLEdBQ0QsYUFBYUMsc0NBQ1hDLFNBQWlCLEVBQ2pCQyxRQUFnQixFQUNoQkMsUUFBZ0IsRUFDaEJDLGNBQXNCLEVBQ3RCQyxNQUFlLEVBQ0c7UUFDbEIsSUFBSTtZQUNGLDhDQUE4QztZQUM5QyxNQUFNQyxXQUFXWCwyREFBY0EsQ0FBQ1ksa0NBQWtDLENBQ2hFTCxVQUNBQyxVQUNBQztZQUdGLHFCQUFxQjtZQUNyQixNQUFNSSxlQUFrQztnQkFDdENDLElBQUlSO2dCQUNKUyxTQUFTSixTQUFTSSxPQUFPO2dCQUN6QkMsYUFBYUwsU0FBU0ssV0FBVztnQkFDakNDLGFBQWFOLFNBQVNNLFdBQVc7Z0JBQ2pDQyxNQUFNO2dCQUNOUjtnQkFDQVMsVUFBVTtvQkFDUlg7b0JBQ0FDO29CQUNBRjtvQkFDQWEsZUFBZUMsS0FBS0MsSUFBSSxDQUN0QixDQUFDLElBQUlDLEtBQUtkLGdCQUFnQmUsT0FBTyxLQUFLLElBQUlELE9BQU9DLE9BQU8sRUFBQyxJQUFNLFFBQU8sS0FBSyxLQUFLLEVBQUM7Z0JBRXJGO1lBQ0Y7WUFFQSxlQUFlO1lBQ2YsT0FBTyxNQUFNdkIscURBQVdBLENBQUN3QixTQUFTLENBQUNaO1FBRXJDLEVBQUUsT0FBT2EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0NBQStDQTtZQUM3RCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYUUsa0NBQ1h0QixTQUFpQixFQUNqQkMsUUFBZ0IsRUFDaEJDLFFBQWdCLEVBQ2hCQyxjQUFzQixFQUN0QkMsTUFBZSxFQUNHO1FBQ2xCLElBQUk7WUFDRiw4Q0FBOEM7WUFDOUMsTUFBTUMsV0FBV1gsMkRBQWNBLENBQUM2Qiw4QkFBOEIsQ0FDNUR0QixVQUNBQyxVQUNBQztZQUdGLHFCQUFxQjtZQUNyQixNQUFNSSxlQUFrQztnQkFDdENDLElBQUlSO2dCQUNKUyxTQUFTSixTQUFTSSxPQUFPO2dCQUN6QkMsYUFBYUwsU0FBU0ssV0FBVztnQkFDakNDLGFBQWFOLFNBQVNNLFdBQVc7Z0JBQ2pDQyxNQUFNO2dCQUNOUjtnQkFDQVMsVUFBVTtvQkFDUlg7b0JBQ0FDO29CQUNBRjtvQkFDQXVCLGdCQUFnQlQsS0FBS0MsSUFBSSxDQUN2QixDQUFDLElBQUlDLEtBQUtkLGdCQUFnQmUsT0FBTyxLQUFLLElBQUlELE9BQU9DLE9BQU8sRUFBQyxJQUFNLFFBQU8sS0FBSyxFQUFDO2dCQUVoRjtZQUNGO1lBRUEsZUFBZTtZQUNmLE9BQU8sTUFBTXZCLHFEQUFXQSxDQUFDd0IsU0FBUyxDQUFDWjtRQUVyQyxFQUFFLE9BQU9hLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHFEQUFxREE7WUFDbkUsT0FBTztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFLLHdCQUNYekIsU0FBaUIsRUFDakJDLFFBQWdCLEVBQ2hCeUIsS0FBYSxFQUNiQyxPQUFlLEVBQ2ZmLE9BQWdFLE9BQU8sRUFDdkVSLE1BQWUsRUFDZndCLE9BQWdCLEVBQ2hCQyxNQUFlLEVBQ0c7UUFDbEIsSUFBSTtZQUNGLHVEQUF1RDtZQUN2RCxNQUFNeEIsV0FBV1gsMkRBQWNBLENBQUNvQyxvQkFBb0IsQ0FDbEQ3QixVQUNBeUIsT0FDQUMsU0FDQUMsU0FDQUM7WUFHRixxQkFBcUI7WUFDckIsTUFBTXRCLGVBQWtDO2dCQUN0Q0MsSUFBSVI7Z0JBQ0pTLFNBQVNKLFNBQVNJLE9BQU87Z0JBQ3pCQyxhQUFhTCxTQUFTSyxXQUFXO2dCQUNqQ0MsYUFBYU4sU0FBU00sV0FBVztnQkFDakNDO2dCQUNBUjtnQkFDQVMsVUFBVTtvQkFDUlo7b0JBQ0F5QjtvQkFDQUM7b0JBQ0FDO29CQUNBQztnQkFDRjtZQUNGO1lBRUEsZUFBZTtZQUNmLE9BQU8sTUFBTWxDLHFEQUFXQSxDQUFDd0IsU0FBUyxDQUFDWjtRQUVyQyxFQUFFLE9BQU9hLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlDQUF5Q0E7WUFDdkQsT0FBTztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFXLHFCQUNYM0IsTUFBYyxFQUNkNEIsUUFBZ0IsRUFBRSxFQUNsQnBCLElBQWEsRUFDcUI7UUFDbEMsT0FBTyxNQUFNaEIscURBQVdBLENBQUNtQyxvQkFBb0IsQ0FBQzNCLFFBQVE0QixPQUFPcEI7SUFDL0Q7SUFFQTs7R0FFQyxHQUNELGFBQWFxQixxQkFDWEMsU0FBa0IsRUFDbEJDLE9BQWdCLEVBQ0s7UUFDckIsT0FBTyxNQUFNdEMsMkRBQWNBLENBQUNvQyxvQkFBb0IsQ0FBQ0MsV0FBV0M7SUFDOUQ7SUFFQTs7R0FFQyxHQUNELGFBQWFDLGdCQUNYRixTQUFrQixFQUNsQkMsT0FBZ0IsRUFDTztRQUN2QixPQUFPLE1BQU10QywyREFBY0EsQ0FBQ3VDLGVBQWUsQ0FBQ0YsV0FBV0M7SUFDekQ7SUFFQTs7R0FFQyxHQUNELGFBQWFFLHlCQUNYQyxTQUFpQixFQUFFLEVBQ25CTixRQUFnQixFQUFFLEVBQ0k7UUFDdEIsT0FBTyxNQUFNckMscURBQVdBLENBQUMwQyx3QkFBd0IsQ0FBQ0MsUUFBUU47SUFDNUQ7SUFFQTs7R0FFQyxHQUNELGFBQWFPLHNCQUNYTCxTQUFrQixFQUNsQkMsT0FBZ0IsRUFPZjtRQUNELE9BQU8sTUFBTXRDLDJEQUFjQSxDQUFDMEMscUJBQXFCLENBQUNMLFdBQVdDO0lBQy9EO0lBRUE7O0dBRUMsR0FDRCxhQUFhSyxvQkFDWFIsUUFBZ0IsRUFBRSxFQUNsQkUsU0FBa0IsRUFDbEJDLE9BQWdCLEVBTWQ7UUFDRixPQUFPLE1BQU10QywyREFBY0EsQ0FBQzJDLG1CQUFtQixDQUFDUixPQUFPRSxXQUFXQztJQUNwRTtJQUVBOztHQUVDLEdBQ0QsYUFBYU0sY0FDWGpDLEVBQVUsRUFDVmtDLGNBQW9CLEVBS25CO1FBQ0QsT0FBTyxNQUFNL0MscURBQVdBLENBQUM4QyxhQUFhLENBQUNqQyxJQUFJa0M7SUFDN0M7SUFFQTs7R0FFQyxHQUNELGFBQWFDLHdCQUlWO1FBQ0QsT0FBTyxNQUFNaEQscURBQVdBLENBQUNnRCxxQkFBcUI7SUFDaEQ7SUFFQTs7R0FFQyxHQUNELGFBQWFDLHdCQUNYQyxhQUFxQixFQUFFLEVBSXRCO1FBQ0QsT0FBTyxNQUFNakQscURBQVdBLENBQUNnRCx1QkFBdUIsQ0FBQ0M7SUFDbkQ7SUFFQTs7R0FFQyxHQUNELGFBQWFDLG1CQUtWO1FBQ0QsSUFBSTtZQUNGLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxhQUFhQyxjQUFjQyxtQkFBbUIsR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ3hGLElBQUksQ0FBQ1QscUJBQXFCO2dCQUMxQixJQUFJLENBQUNWLG9CQUFvQixDQUN2QixJQUFJaEIsS0FBS0EsS0FBS29DLEdBQUcsS0FBSyxJQUFJLEtBQUssS0FBSyxLQUFLLE1BQU1DLFdBQVcsSUFDMUQsSUFBSXJDLE9BQU9xQyxXQUFXO2dCQUV4QixJQUFJLENBQUNsQixlQUFlLENBQ2xCLElBQUluQixLQUFLQSxLQUFLb0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLEtBQUssTUFBTUMsV0FBVyxJQUMxRCxJQUFJckMsT0FBT3FDLFdBQVc7Z0JBRXhCLElBQUksQ0FBQ2YscUJBQXFCLENBQ3hCLElBQUl0QixLQUFLQSxLQUFLb0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLEtBQUssTUFBTUMsV0FBVyxJQUMxRCxJQUFJckMsT0FBT3FDLFdBQVc7YUFFekI7WUFFRCxPQUFPO2dCQUNMUDtnQkFDQUM7Z0JBQ0FDO2dCQUNBQztZQUNGO1FBRUYsRUFBRSxPQUFPOUIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUNBQXlDQTtZQUN2RCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7O0dBR0MsR0FDRCxhQUFhbUMsaUNBQ1h2RCxTQUFpQixFQUNqQkMsUUFBZ0IsRUFDaEJ1RCxTQUFpQixFQUNqQkMsaUJBQXlCLEVBQ1A7UUFDbEIsSUFBSTtZQUNGcEMsUUFBUXFDLEdBQUcsQ0FBQyxDQUFDLDJEQUEyRCxFQUFFMUQsV0FBVztZQUVyRiw4Q0FBOEM7WUFDOUMsTUFBTUssV0FBV1gsMkRBQWNBLENBQUNpRSwrQkFBK0IsQ0FDN0QxRCxVQUNBdUQsV0FDQUM7WUFHRixxQkFBcUI7WUFDckIsTUFBTWxELGVBQWtDO2dCQUN0Q0MsSUFBSVI7Z0JBQ0pTLFNBQVNKLFNBQVNJLE9BQU87Z0JBQ3pCQyxhQUFhTCxTQUFTSyxXQUFXO2dCQUNqQ0MsYUFBYU4sU0FBU00sV0FBVztnQkFDakNDLE1BQU07Z0JBQ05DLFVBQVU7b0JBQ1IyQztvQkFDQUM7b0JBQ0F4RDtvQkFDQTJELGdCQUFnQjtvQkFDaEJDLFFBQVEsSUFBSTVDLE9BQU9xQyxXQUFXO2dCQUNoQztZQUNGO1lBRUEsZUFBZTtZQUNmLE1BQU1RLFNBQVMsTUFBTW5FLHFEQUFXQSxDQUFDd0IsU0FBUyxDQUFDWjtZQUUzQyxJQUFJdUQsUUFBUTtnQkFDVnpDLFFBQVFxQyxHQUFHLENBQUMsQ0FBQywyREFBMkQsRUFBRTFELFdBQVc7WUFDdkYsT0FBTztnQkFDTHFCLFFBQVFELEtBQUssQ0FBQyxDQUFDLHFEQUFxRCxFQUFFcEIsV0FBVztZQUNuRjtZQUVBLE9BQU84RDtRQUVULEVBQUUsT0FBTzFDLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlFQUFpRUE7WUFDL0UsT0FBTztRQUNUO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxzcmNcXGxpYlxcc2VydmljZXNcXGVtYWlsXFxlbWFpbE5vdGlmaWNhdGlvblNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2xpYi9zZXJ2aWNlcy9lbWFpbC9lbWFpbE5vdGlmaWNhdGlvblNlcnZpY2UudHNcbi8vIFNlcnZpY2lvIHByaW5jaXBhbCBkZSBub3RpZmljYWNpb25lcyBwb3IgZW1haWwgKEFQSSBww7pibGljYSlcblxuaW1wb3J0IHsgRW1haWxOb3RpZmljYXRpb24sIEVtYWlsU3RhdHMsIEZhaWx1cmVTdGF0cywgVXNlck5vdGlmaWNhdGlvbnNSZXN1bHQsIFJldHJ5UmVzdWx0IH0gZnJvbSAnLi90eXBlcyc7XG5pbXBvcnQgeyBFbWFpbFRlbXBsYXRlcyB9IGZyb20gJy4vZW1haWxUZW1wbGF0ZXMnO1xuaW1wb3J0IHsgRW1haWxTZW5kZXIgfSBmcm9tICcuL2VtYWlsU2VuZGVyJztcbmltcG9ydCB7IEVtYWlsTG9nZ2VyIH0gZnJvbSAnLi9lbWFpbExvZ2dlcic7XG5pbXBvcnQgeyBFbWFpbEFuYWx5dGljcyB9IGZyb20gJy4vZW1haWxBbmFseXRpY3MnO1xuXG5leHBvcnQgY2xhc3MgRW1haWxOb3RpZmljYXRpb25TZXJ2aWNlIHtcbiAgXG4gIC8qKlxuICAgKiBFbnZpYXIgbm90aWZpY2FjacOzbiBkZSBjYW5jZWxhY2nDs24gZGUgc3VzY3JpcGNpw7NuXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgc2VuZFN1YnNjcmlwdGlvbkNhbmNlbGxlZE5vdGlmaWNhdGlvbihcbiAgICB1c2VyRW1haWw6IHN0cmluZyxcbiAgICB1c2VyTmFtZTogc3RyaW5nLFxuICAgIHBsYW5OYW1lOiBzdHJpbmcsXG4gICAgZ3JhY2VQZXJpb2RFbmQ6IHN0cmluZyxcbiAgICB1c2VySWQ/OiBzdHJpbmdcbiAgKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdlbmVyYXIgY29udGVuaWRvIGRlbCBlbWFpbCB1c2FuZG8gdGVtcGxhdGVcbiAgICAgIGNvbnN0IHRlbXBsYXRlID0gRW1haWxUZW1wbGF0ZXMuZ2VuZXJhdGVTdWJzY3JpcHRpb25DYW5jZWxsZWRFbWFpbChcbiAgICAgICAgdXNlck5hbWUsXG4gICAgICAgIHBsYW5OYW1lLFxuICAgICAgICBncmFjZVBlcmlvZEVuZFxuICAgICAgKTtcblxuICAgICAgLy8gQ3JlYXIgbm90aWZpY2FjacOzblxuICAgICAgY29uc3Qgbm90aWZpY2F0aW9uOiBFbWFpbE5vdGlmaWNhdGlvbiA9IHtcbiAgICAgICAgdG86IHVzZXJFbWFpbCxcbiAgICAgICAgc3ViamVjdDogdGVtcGxhdGUuc3ViamVjdCxcbiAgICAgICAgaHRtbENvbnRlbnQ6IHRlbXBsYXRlLmh0bWxDb250ZW50LFxuICAgICAgICB0ZXh0Q29udGVudDogdGVtcGxhdGUudGV4dENvbnRlbnQsXG4gICAgICAgIHR5cGU6ICdzdWJzY3JpcHRpb25fY2FuY2VsbGVkJyxcbiAgICAgICAgdXNlcklkLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgICAgICAgIHBsYW5OYW1lLFxuICAgICAgICAgIGdyYWNlUGVyaW9kRW5kLFxuICAgICAgICAgIHVzZXJOYW1lLFxuICAgICAgICAgIGRheXNSZW1haW5pbmc6IE1hdGguY2VpbChcbiAgICAgICAgICAgIChuZXcgRGF0ZShncmFjZVBlcmlvZEVuZCkuZ2V0VGltZSgpIC0gbmV3IERhdGUoKS5nZXRUaW1lKCkpIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgICAvLyBFbnZpYXIgZW1haWxcbiAgICAgIHJldHVybiBhd2FpdCBFbWFpbFNlbmRlci5zZW5kRW1haWwobm90aWZpY2F0aW9uKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBlbnZpYW5kbyBub3RpZmljYWNpw7NuIGRlIGNhbmNlbGFjacOzbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEVudmlhciByZWNvcmRhdG9yaW8gZGUgcXVlIGVsIHBlcsOtb2RvIGRlIGdyYWNpYSBlc3TDoSBwb3IgdGVybWluYXJcbiAgICovXG4gIHN0YXRpYyBhc3luYyBzZW5kR3JhY2VQZXJpb2RFbmRpbmdOb3RpZmljYXRpb24oXG4gICAgdXNlckVtYWlsOiBzdHJpbmcsXG4gICAgdXNlck5hbWU6IHN0cmluZyxcbiAgICBwbGFuTmFtZTogc3RyaW5nLFxuICAgIGdyYWNlUGVyaW9kRW5kOiBzdHJpbmcsXG4gICAgdXNlcklkPzogc3RyaW5nXG4gICk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBHZW5lcmFyIGNvbnRlbmlkbyBkZWwgZW1haWwgdXNhbmRvIHRlbXBsYXRlXG4gICAgICBjb25zdCB0ZW1wbGF0ZSA9IEVtYWlsVGVtcGxhdGVzLmdlbmVyYXRlR3JhY2VQZXJpb2RFbmRpbmdFbWFpbChcbiAgICAgICAgdXNlck5hbWUsXG4gICAgICAgIHBsYW5OYW1lLFxuICAgICAgICBncmFjZVBlcmlvZEVuZFxuICAgICAgKTtcblxuICAgICAgLy8gQ3JlYXIgbm90aWZpY2FjacOzblxuICAgICAgY29uc3Qgbm90aWZpY2F0aW9uOiBFbWFpbE5vdGlmaWNhdGlvbiA9IHtcbiAgICAgICAgdG86IHVzZXJFbWFpbCxcbiAgICAgICAgc3ViamVjdDogdGVtcGxhdGUuc3ViamVjdCxcbiAgICAgICAgaHRtbENvbnRlbnQ6IHRlbXBsYXRlLmh0bWxDb250ZW50LFxuICAgICAgICB0ZXh0Q29udGVudDogdGVtcGxhdGUudGV4dENvbnRlbnQsXG4gICAgICAgIHR5cGU6ICdncmFjZV9wZXJpb2RfZW5kaW5nJyxcbiAgICAgICAgdXNlcklkLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgICAgICAgIHBsYW5OYW1lLFxuICAgICAgICAgIGdyYWNlUGVyaW9kRW5kLFxuICAgICAgICAgIHVzZXJOYW1lLFxuICAgICAgICAgIGhvdXJzUmVtYWluaW5nOiBNYXRoLmNlaWwoXG4gICAgICAgICAgICAobmV3IERhdGUoZ3JhY2VQZXJpb2RFbmQpLmdldFRpbWUoKSAtIG5ldyBEYXRlKCkuZ2V0VGltZSgpKSAvICgxMDAwICogNjAgKiA2MClcbiAgICAgICAgICApXG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIC8vIEVudmlhciBlbWFpbFxuICAgICAgcmV0dXJuIGF3YWl0IEVtYWlsU2VuZGVyLnNlbmRFbWFpbChub3RpZmljYXRpb24pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGVudmlhbmRvIHJlY29yZGF0b3JpbyBkZSBwZXLDrW9kbyBkZSBncmFjaWE6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBFbnZpYXIgbm90aWZpY2FjacOzbiBnZW7DqXJpY2FcbiAgICovXG4gIHN0YXRpYyBhc3luYyBzZW5kR2VuZXJpY05vdGlmaWNhdGlvbihcbiAgICB1c2VyRW1haWw6IHN0cmluZyxcbiAgICB1c2VyTmFtZTogc3RyaW5nLFxuICAgIHRpdGxlOiBzdHJpbmcsXG4gICAgbWVzc2FnZTogc3RyaW5nLFxuICAgIHR5cGU6ICdwbGFuX2V4cGlyZWQnIHwgJ3BheW1lbnRfZmFpbGVkJyB8ICd3ZWxjb21lJyB8ICdvdGhlcicgPSAnb3RoZXInLFxuICAgIHVzZXJJZD86IHN0cmluZyxcbiAgICBjdGFUZXh0Pzogc3RyaW5nLFxuICAgIGN0YVVybD86IHN0cmluZ1xuICApOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgLy8gR2VuZXJhciBjb250ZW5pZG8gZGVsIGVtYWlsIHVzYW5kbyB0ZW1wbGF0ZSBnZW7DqXJpY29cbiAgICAgIGNvbnN0IHRlbXBsYXRlID0gRW1haWxUZW1wbGF0ZXMuZ2VuZXJhdGVHZW5lcmljRW1haWwoXG4gICAgICAgIHVzZXJOYW1lLFxuICAgICAgICB0aXRsZSxcbiAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgY3RhVGV4dCxcbiAgICAgICAgY3RhVXJsXG4gICAgICApO1xuXG4gICAgICAvLyBDcmVhciBub3RpZmljYWNpw7NuXG4gICAgICBjb25zdCBub3RpZmljYXRpb246IEVtYWlsTm90aWZpY2F0aW9uID0ge1xuICAgICAgICB0bzogdXNlckVtYWlsLFxuICAgICAgICBzdWJqZWN0OiB0ZW1wbGF0ZS5zdWJqZWN0LFxuICAgICAgICBodG1sQ29udGVudDogdGVtcGxhdGUuaHRtbENvbnRlbnQsXG4gICAgICAgIHRleHRDb250ZW50OiB0ZW1wbGF0ZS50ZXh0Q29udGVudCxcbiAgICAgICAgdHlwZSxcbiAgICAgICAgdXNlcklkLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgICAgICAgIHVzZXJOYW1lLFxuICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgIG1lc3NhZ2UsXG4gICAgICAgICAgY3RhVGV4dCxcbiAgICAgICAgICBjdGFVcmxcbiAgICAgICAgfVxuICAgICAgfTtcblxuICAgICAgLy8gRW52aWFyIGVtYWlsXG4gICAgICByZXR1cm4gYXdhaXQgRW1haWxTZW5kZXIuc2VuZEVtYWlsKG5vdGlmaWNhdGlvbik7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZW52aWFuZG8gbm90aWZpY2FjacOzbiBnZW7DqXJpY2E6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBPYnRlbmVyIGhpc3RvcmlhbCBkZSBub3RpZmljYWNpb25lcyBkZSB1biB1c3VhcmlvXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0VXNlck5vdGlmaWNhdGlvbnMoXG4gICAgdXNlcklkOiBzdHJpbmcsXG4gICAgbGltaXQ6IG51bWJlciA9IDUwLFxuICAgIHR5cGU/OiBzdHJpbmdcbiAgKTogUHJvbWlzZTxVc2VyTm90aWZpY2F0aW9uc1Jlc3VsdD4ge1xuICAgIHJldHVybiBhd2FpdCBFbWFpbExvZ2dlci5nZXRVc2VyTm90aWZpY2F0aW9ucyh1c2VySWQsIGxpbWl0LCB0eXBlKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBPYnRlbmVyIGVzdGFkw61zdGljYXMgZGUgbm90aWZpY2FjaW9uZXMgcG9yIHRpcG9cbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXROb3RpZmljYXRpb25TdGF0cyhcbiAgICBzdGFydERhdGU/OiBzdHJpbmcsXG4gICAgZW5kRGF0ZT86IHN0cmluZ1xuICApOiBQcm9taXNlPEVtYWlsU3RhdHM+IHtcbiAgICByZXR1cm4gYXdhaXQgRW1haWxBbmFseXRpY3MuZ2V0Tm90aWZpY2F0aW9uU3RhdHMoc3RhcnREYXRlLCBlbmREYXRlKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBPYnRlbmVyIGVzdGFkw61zdGljYXMgZGUgZmFsbG9zIHkgZXJyb3Jlc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldEZhaWx1cmVTdGF0cyhcbiAgICBzdGFydERhdGU/OiBzdHJpbmcsXG4gICAgZW5kRGF0ZT86IHN0cmluZ1xuICApOiBQcm9taXNlPEZhaWx1cmVTdGF0cz4ge1xuICAgIHJldHVybiBhd2FpdCBFbWFpbEFuYWx5dGljcy5nZXRGYWlsdXJlU3RhdHMoc3RhcnREYXRlLCBlbmREYXRlKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBSZWVudmlhciBub3RpZmljYWNpb25lcyBmYWxsaWRhc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIHJldHJ5RmFpbGVkTm90aWZpY2F0aW9ucyhcbiAgICBtYXhBZ2U6IG51bWJlciA9IDI0LFxuICAgIGxpbWl0OiBudW1iZXIgPSAxMFxuICApOiBQcm9taXNlPFJldHJ5UmVzdWx0PiB7XG4gICAgcmV0dXJuIGF3YWl0IEVtYWlsU2VuZGVyLnJldHJ5RmFpbGVkTm90aWZpY2F0aW9ucyhtYXhBZ2UsIGxpbWl0KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBPYnRlbmVyIG3DqXRyaWNhcyBkZSByZW5kaW1pZW50b1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFBlcmZvcm1hbmNlTWV0cmljcyhcbiAgICBzdGFydERhdGU/OiBzdHJpbmcsXG4gICAgZW5kRGF0ZT86IHN0cmluZ1xuICApOiBQcm9taXNlPHtcbiAgICB0b3RhbFNlbnQ6IG51bWJlcjtcbiAgICBzdWNjZXNzUmF0ZTogbnVtYmVyO1xuICAgIGF2Z1Jlc3BvbnNlVGltZTogbnVtYmVyO1xuICAgIHBlYWtIb3VyczogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgICBkYWlseVZvbHVtZTogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgfT4ge1xuICAgIHJldHVybiBhd2FpdCBFbWFpbEFuYWx5dGljcy5nZXRQZXJmb3JtYW5jZU1ldHJpY3Moc3RhcnREYXRlLCBlbmREYXRlKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBPYnRlbmVyIHRvcCB1c3VhcmlvcyBwb3Igdm9sdW1lbiBkZSBub3RpZmljYWNpb25lc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFRvcFVzZXJzQnlWb2x1bWUoXG4gICAgbGltaXQ6IG51bWJlciA9IDEwLFxuICAgIHN0YXJ0RGF0ZT86IHN0cmluZyxcbiAgICBlbmREYXRlPzogc3RyaW5nXG4gICk6IFByb21pc2U8QXJyYXk8e1xuICAgIHVzZXJJZDogc3RyaW5nO1xuICAgIGVtYWlsOiBzdHJpbmc7XG4gICAgY291bnQ6IG51bWJlcjtcbiAgICBsYXN0Tm90aWZpY2F0aW9uOiBzdHJpbmc7XG4gIH0+PiB7XG4gICAgcmV0dXJuIGF3YWl0IEVtYWlsQW5hbHl0aWNzLmdldFRvcFVzZXJzQnlWb2x1bWUobGltaXQsIHN0YXJ0RGF0ZSwgZW5kRGF0ZSk7XG4gIH1cblxuICAvKipcbiAgICogRW52aWFyIGVtYWlsIGRlIHBydWViYVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHNlbmRUZXN0RW1haWwoXG4gICAgdG86IHN0cmluZyxcbiAgICBwcm92aWRlckNvbmZpZz86IGFueVxuICApOiBQcm9taXNlPHtcbiAgICBzdWNjZXNzOiBib29sZWFuO1xuICAgIG1lc3NhZ2U6IHN0cmluZztcbiAgICBkZXRhaWxzPzogYW55O1xuICB9PiB7XG4gICAgcmV0dXJuIGF3YWl0IEVtYWlsU2VuZGVyLnNlbmRUZXN0RW1haWwodG8sIHByb3ZpZGVyQ29uZmlnKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBWYWxpZGFyIGNvbmZpZ3VyYWNpw7NuIGRlbCBwcm92ZWVkb3IgZGUgZW1haWxcbiAgICovXG4gIHN0YXRpYyBhc3luYyB2YWxpZGF0ZUVtYWlsUHJvdmlkZXIoKTogUHJvbWlzZTx7XG4gICAgaXNWYWxpZDogYm9vbGVhbjtcbiAgICBwcm92aWRlcjogc3RyaW5nO1xuICAgIG1lc3NhZ2U6IHN0cmluZztcbiAgfT4ge1xuICAgIHJldHVybiBhd2FpdCBFbWFpbFNlbmRlci52YWxpZGF0ZUVtYWlsUHJvdmlkZXIoKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBMaW1waWFyIG5vdGlmaWNhY2lvbmVzIGFudGlndWFzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgY2xlYW51cE9sZE5vdGlmaWNhdGlvbnMoXG4gICAgZGF5c1RvS2VlcDogbnVtYmVyID0gOTBcbiAgKTogUHJvbWlzZTx7XG4gICAgZGVsZXRlZDogbnVtYmVyO1xuICAgIGVycm9yPzogc3RyaW5nO1xuICB9PiB7XG4gICAgcmV0dXJuIGF3YWl0IEVtYWlsTG9nZ2VyLmNsZWFudXBPbGROb3RpZmljYXRpb25zKGRheXNUb0tlZXApO1xuICB9XG5cbiAgLyoqXG4gICAqIE9idGVuZXIgcmVzdW1lbiBkZWwgc2lzdGVtYSBkZSBub3RpZmljYWNpb25lc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFN5c3RlbVN1bW1hcnkoKTogUHJvbWlzZTx7XG4gICAgcHJvdmlkZXJTdGF0dXM6IGFueTtcbiAgICByZWNlbnRTdGF0czogRW1haWxTdGF0cztcbiAgICBmYWlsdXJlU3RhdHM6IEZhaWx1cmVTdGF0cztcbiAgICBwZXJmb3JtYW5jZU1ldHJpY3M6IGFueTtcbiAgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBbcHJvdmlkZXJTdGF0dXMsIHJlY2VudFN0YXRzLCBmYWlsdXJlU3RhdHMsIHBlcmZvcm1hbmNlTWV0cmljc10gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgIHRoaXMudmFsaWRhdGVFbWFpbFByb3ZpZGVyKCksXG4gICAgICAgIHRoaXMuZ2V0Tm90aWZpY2F0aW9uU3RhdHMoXG4gICAgICAgICAgbmV3IERhdGUoRGF0ZS5ub3coKSAtIDcgKiAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpLCAvLyDDmmx0aW1vcyA3IGTDrWFzXG4gICAgICAgICAgbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICksXG4gICAgICAgIHRoaXMuZ2V0RmFpbHVyZVN0YXRzKFxuICAgICAgICAgIG5ldyBEYXRlKERhdGUubm93KCkgLSA3ICogMjQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgKSxcbiAgICAgICAgdGhpcy5nZXRQZXJmb3JtYW5jZU1ldHJpY3MoXG4gICAgICAgICAgbmV3IERhdGUoRGF0ZS5ub3coKSAtIDcgKiAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICApXG4gICAgICBdKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcHJvdmlkZXJTdGF0dXMsXG4gICAgICAgIHJlY2VudFN0YXRzLFxuICAgICAgICBmYWlsdXJlU3RhdHMsXG4gICAgICAgIHBlcmZvcm1hbmNlTWV0cmljc1xuICAgICAgfTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBvYnRlbmllbmRvIHJlc3VtZW4gZGVsIHNpc3RlbWE6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEVudmlhciBlbWFpbCBkZSBjb25maXJtYWNpw7NuIHBhcmEgdXBncmFkZSBkZSBwbGFuXG4gICAqIFNFR1VSSURBRDogRXN0ZSBlbWFpbCBzZSBlbnbDrWEgY3VhbmRvIHNlIGRldGVjdGEgdW4gcGFnbyBwYXJhIHVuIGVtYWlsIGV4aXN0ZW50ZVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHNlbmRQbGFuVXBncmFkZUNvbmZpcm1hdGlvbkVtYWlsKFxuICAgIHVzZXJFbWFpbDogc3RyaW5nLFxuICAgIHVzZXJOYW1lOiBzdHJpbmcsXG4gICAgbmV3UGxhbklkOiBzdHJpbmcsXG4gICAgY29uZmlybWF0aW9uVG9rZW46IHN0cmluZ1xuICApOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coYPCfk6cgW1NFQ1VSSVRZXSBFbnZpYW5kbyBlbWFpbCBkZSBjb25maXJtYWNpw7NuIGRlIHVwZ3JhZGUgYTogJHt1c2VyRW1haWx9YCk7XG5cbiAgICAgIC8vIEdlbmVyYXIgY29udGVuaWRvIGRlbCBlbWFpbCB1c2FuZG8gdGVtcGxhdGVcbiAgICAgIGNvbnN0IHRlbXBsYXRlID0gRW1haWxUZW1wbGF0ZXMuZ2VuZXJhdGVQbGFuVXBncmFkZUNvbmZpcm1hdGlvbihcbiAgICAgICAgdXNlck5hbWUsXG4gICAgICAgIG5ld1BsYW5JZCxcbiAgICAgICAgY29uZmlybWF0aW9uVG9rZW5cbiAgICAgICk7XG5cbiAgICAgIC8vIENyZWFyIG5vdGlmaWNhY2nDs25cbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbjogRW1haWxOb3RpZmljYXRpb24gPSB7XG4gICAgICAgIHRvOiB1c2VyRW1haWwsXG4gICAgICAgIHN1YmplY3Q6IHRlbXBsYXRlLnN1YmplY3QsXG4gICAgICAgIGh0bWxDb250ZW50OiB0ZW1wbGF0ZS5odG1sQ29udGVudCxcbiAgICAgICAgdGV4dENvbnRlbnQ6IHRlbXBsYXRlLnRleHRDb250ZW50LFxuICAgICAgICB0eXBlOiAncGxhbl91cGdyYWRlX2NvbmZpcm1hdGlvbicsXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgICAgbmV3UGxhbklkLFxuICAgICAgICAgIGNvbmZpcm1hdGlvblRva2VuLFxuICAgICAgICAgIHVzZXJOYW1lLFxuICAgICAgICAgIHNlY3VyaXR5QWN0aW9uOiB0cnVlLFxuICAgICAgICAgIHNlbnRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIC8vIEVudmlhciBlbWFpbFxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgRW1haWxTZW5kZXIuc2VuZEVtYWlsKG5vdGlmaWNhdGlvbik7XG5cbiAgICAgIGlmIChyZXN1bHQpIHtcbiAgICAgICAgY29uc29sZS5sb2coYOKchSBbU0VDVVJJVFldIEVtYWlsIGRlIGNvbmZpcm1hY2nDs24gZW52aWFkbyBleGl0b3NhbWVudGUgYTogJHt1c2VyRW1haWx9YCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKGDinYwgW1NFQ1VSSVRZXSBFcnJvciBlbnZpYW5kbyBlbWFpbCBkZSBjb25maXJtYWNpw7NuIGE6ICR7dXNlckVtYWlsfWApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gcmVzdWx0O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbU0VDVVJJVFldIEVycm9yIGVudmlhbmRvIGVtYWlsIGRlIGNvbmZpcm1hY2nDs24gZGUgdXBncmFkZTonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiRW1haWxUZW1wbGF0ZXMiLCJFbWFpbFNlbmRlciIsIkVtYWlsTG9nZ2VyIiwiRW1haWxBbmFseXRpY3MiLCJFbWFpbE5vdGlmaWNhdGlvblNlcnZpY2UiLCJzZW5kU3Vic2NyaXB0aW9uQ2FuY2VsbGVkTm90aWZpY2F0aW9uIiwidXNlckVtYWlsIiwidXNlck5hbWUiLCJwbGFuTmFtZSIsImdyYWNlUGVyaW9kRW5kIiwidXNlcklkIiwidGVtcGxhdGUiLCJnZW5lcmF0ZVN1YnNjcmlwdGlvbkNhbmNlbGxlZEVtYWlsIiwibm90aWZpY2F0aW9uIiwidG8iLCJzdWJqZWN0IiwiaHRtbENvbnRlbnQiLCJ0ZXh0Q29udGVudCIsInR5cGUiLCJtZXRhZGF0YSIsImRheXNSZW1haW5pbmciLCJNYXRoIiwiY2VpbCIsIkRhdGUiLCJnZXRUaW1lIiwic2VuZEVtYWlsIiwiZXJyb3IiLCJjb25zb2xlIiwic2VuZEdyYWNlUGVyaW9kRW5kaW5nTm90aWZpY2F0aW9uIiwiZ2VuZXJhdGVHcmFjZVBlcmlvZEVuZGluZ0VtYWlsIiwiaG91cnNSZW1haW5pbmciLCJzZW5kR2VuZXJpY05vdGlmaWNhdGlvbiIsInRpdGxlIiwibWVzc2FnZSIsImN0YVRleHQiLCJjdGFVcmwiLCJnZW5lcmF0ZUdlbmVyaWNFbWFpbCIsImdldFVzZXJOb3RpZmljYXRpb25zIiwibGltaXQiLCJnZXROb3RpZmljYXRpb25TdGF0cyIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJnZXRGYWlsdXJlU3RhdHMiLCJyZXRyeUZhaWxlZE5vdGlmaWNhdGlvbnMiLCJtYXhBZ2UiLCJnZXRQZXJmb3JtYW5jZU1ldHJpY3MiLCJnZXRUb3BVc2Vyc0J5Vm9sdW1lIiwic2VuZFRlc3RFbWFpbCIsInByb3ZpZGVyQ29uZmlnIiwidmFsaWRhdGVFbWFpbFByb3ZpZGVyIiwiY2xlYW51cE9sZE5vdGlmaWNhdGlvbnMiLCJkYXlzVG9LZWVwIiwiZ2V0U3lzdGVtU3VtbWFyeSIsInByb3ZpZGVyU3RhdHVzIiwicmVjZW50U3RhdHMiLCJmYWlsdXJlU3RhdHMiLCJwZXJmb3JtYW5jZU1ldHJpY3MiLCJQcm9taXNlIiwiYWxsIiwibm93IiwidG9JU09TdHJpbmciLCJzZW5kUGxhblVwZ3JhZGVDb25maXJtYXRpb25FbWFpbCIsIm5ld1BsYW5JZCIsImNvbmZpcm1hdGlvblRva2VuIiwibG9nIiwiZ2VuZXJhdGVQbGFuVXBncmFkZUNvbmZpcm1hdGlvbiIsInNlY3VyaXR5QWN0aW9uIiwic2VudEF0IiwicmVzdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailNotificationService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailSender.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/email/emailSender.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailSender: () => (/* binding */ EmailSender)\n/* harmony export */ });\n/* harmony import */ var _emailLogger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./emailLogger */ \"(rsc)/./src/lib/services/email/emailLogger.ts\");\n// src/lib/services/email/emailSender.ts\n// Lógica de envío de emails con reintentos y manejo de errores\n\nclass EmailSender {\n    /**\n   * Función base para enviar emails con reintentos y manejo de errores\n   * NOTA: Implementar con tu proveedor de email preferido (SendGrid, Resend, etc.)\n   */ static async sendEmail(notification, retryCount = 0) {\n        const maxRetries = 3;\n        const retryDelay = Math.pow(2, retryCount) * 1000; // Backoff exponencial: 1s, 2s, 4s\n        let notificationId = null;\n        try {\n            console.log(`📧 Enviando email (intento ${retryCount + 1}/${maxRetries + 1}):`, {\n                to: notification.to,\n                subject: notification.subject,\n                type: notification.type\n            });\n            // Registrar notificación como 'pending' antes del envío\n            notificationId = await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.logEmailNotification(notification, 'pending');\n            // TODO: Implementar con tu proveedor de email real\n            // Ejemplo con Resend:\n            /*\n      const resend = new Resend(process.env.RESEND_API_KEY);\n      const result = await resend.emails.send({\n        from: 'OposI <<EMAIL>>',\n        to: notification.to,\n        subject: notification.subject,\n        html: notification.htmlContent,\n        text: notification.textContent,\n      });\n      \n      if (result.error) {\n        throw new Error(`Resend API error: ${result.error.message}`);\n      }\n      */ // Simulación de envío (remover cuando implementes proveedor real)\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            // Simular fallo ocasional para testing (remover en producción)\n            if (Math.random() < 0.1 && retryCount === 0) {\n                throw new Error('Simulated email provider error');\n            }\n            // Actualizar estado a 'sent' si el envío fue exitoso\n            if (notificationId) {\n                await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.updateEmailNotificationStatus(notificationId, 'sent');\n            }\n            console.log('✅ Email enviado exitosamente');\n            return true;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`❌ Error enviando email (intento ${retryCount + 1}):`, errorMessage);\n            // Actualizar estado a 'failed' si tenemos el ID\n            if (notificationId) {\n                await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.updateEmailNotificationStatus(notificationId, 'failed', errorMessage);\n            }\n            // Intentar reenvío si no hemos alcanzado el máximo de reintentos\n            if (retryCount < maxRetries) {\n                console.log(`🔄 Reintentando envío en ${retryDelay}ms...`);\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                return this.sendEmail(notification, retryCount + 1);\n            }\n            // Si agotamos los reintentos, registrar fallo final\n            console.error(`💥 Fallo definitivo después de ${maxRetries + 1} intentos`);\n            return false;\n        }\n    }\n    /**\n   * Reenviar notificaciones fallidas\n   */ static async retryFailedNotifications(maxAge = 24, limit = 10 // Máximo 10 reintentos por ejecución\n    ) {\n        try {\n            console.log(`🔄 Buscando notificaciones fallidas para reintentar (máximo ${maxAge} horas)...`);\n            // Obtener notificaciones fallidas\n            const failedNotifications = await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.getFailedNotifications(maxAge, limit);\n            if (failedNotifications.length === 0) {\n                console.log('✅ No se encontraron notificaciones fallidas para reintentar');\n                return {\n                    attempted: 0,\n                    successful: 0,\n                    failed: 0,\n                    errors: []\n                };\n            }\n            console.log(`📋 Encontradas ${failedNotifications.length} notificaciones para reintentar`);\n            let successful = 0;\n            let failed = 0;\n            const errors = [];\n            // Reintentar cada notificación\n            for (const notification of failedNotifications){\n                try {\n                    const emailNotification = {\n                        to: notification.recipient_email,\n                        subject: notification.subject,\n                        htmlContent: '',\n                        textContent: '',\n                        type: notification.type,\n                        userId: notification.user_id,\n                        metadata: notification.metadata\n                    };\n                    // Marcar como reintento en metadata\n                    emailNotification.metadata = {\n                        ...emailNotification.metadata,\n                        retry_attempt: true,\n                        original_notification_id: notification.id,\n                        retry_at: new Date().toISOString()\n                    };\n                    const success = await this.sendEmail(emailNotification);\n                    if (success) {\n                        successful++;\n                        // Marcar la notificación original como reintentada exitosamente\n                        await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, true);\n                    } else {\n                        failed++;\n                        await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, false, 'Retry failed');\n                        errors.push(`Failed to retry notification ${notification.id}`);\n                    }\n                } catch (retryError) {\n                    failed++;\n                    const errorMsg = `Error retrying notification ${notification.id}: ${retryError instanceof Error ? retryError.message : 'Unknown error'}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                    await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, false, errorMsg);\n                }\n            }\n            console.log(`🎯 Reintentos completados: ${successful} exitosos, ${failed} fallidos`);\n            return {\n                attempted: failedNotifications.length,\n                successful,\n                failed,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en retryFailedNotifications:', error);\n            throw error;\n        }\n    }\n    /**\n   * Enviar email de prueba para verificar configuración\n   */ static async sendTestEmail(to, providerConfig) {\n        try {\n            const testNotification = {\n                to,\n                subject: 'Test Email - OposI',\n                htmlContent: `\n          <h1>Email de Prueba</h1>\n          <p>Este es un email de prueba para verificar la configuración del sistema de notificaciones.</p>\n          <p>Enviado el: ${new Date().toLocaleString('es-ES')}</p>\n        `,\n                textContent: `\n          Email de Prueba\n          \n          Este es un email de prueba para verificar la configuración del sistema de notificaciones.\n          Enviado el: ${new Date().toLocaleString('es-ES')}\n        `,\n                type: 'other',\n                metadata: {\n                    test_email: true,\n                    sent_at: new Date().toISOString()\n                }\n            };\n            const success = await this.sendEmail(testNotification);\n            return {\n                success,\n                message: success ? 'Email de prueba enviado exitosamente' : 'Fallo al enviar email de prueba',\n                details: {\n                    to,\n                    timestamp: new Date().toISOString()\n                }\n            };\n        } catch (error) {\n            console.error('Error enviando email de prueba:', error);\n            return {\n                success: false,\n                message: 'Error enviando email de prueba',\n                details: {\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                }\n            };\n        }\n    }\n    /**\n   * Validar configuración del proveedor de email\n   */ static async validateEmailProvider() {\n        try {\n            // TODO: Implementar validación específica del proveedor\n            // Ejemplo para Resend:\n            /*\n      if (!process.env.RESEND_API_KEY) {\n        return {\n          isValid: false,\n          provider: 'Resend',\n          message: 'RESEND_API_KEY no configurada'\n        };\n      }\n      \n      // Probar conexión con API\n      const resend = new Resend(process.env.RESEND_API_KEY);\n      await resend.domains.list();\n      */ // Por ahora, simulación\n            return {\n                isValid: true,\n                provider: 'Simulado',\n                message: 'Proveedor de email configurado correctamente'\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                provider: 'Unknown',\n                message: error instanceof Error ? error.message : 'Error validando proveedor'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailSender.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailTemplates.ts":
/*!**************************************************!*\
  !*** ./src/lib/services/email/emailTemplates.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailTemplates: () => (/* binding */ EmailTemplates)\n/* harmony export */ });\n// src/lib/services/email/emailTemplates.ts\n// Templates para diferentes tipos de notificaciones por email\nclass EmailTemplates {\n    /**\n   * Template para notificación de cancelación de suscripción\n   */ static generateSubscriptionCancelledEmail(userName, planName, gracePeriodEnd) {\n        const gracePeriodDate = new Date(gracePeriodEnd);\n        const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        const daysRemaining = Math.ceil((gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Suscripción Cancelada - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #2563eb;\">Suscripción Cancelada</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>Hemos recibido tu solicitud de cancelación de la suscripción al <strong>Plan ${planName}</strong>.</p>\n          \n          <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n            <h3 style=\"margin-top: 0; color: #059669;\">📅 Período de Gracia Activo</h3>\n            <p><strong>Mantienes acceso completo hasta:</strong> ${formattedDate}</p>\n            <p><strong>Días restantes:</strong> ${daysRemaining} días</p>\n            <p>Durante este período, puedes seguir usando todas las funciones de tu plan actual.</p>\n          </div>\n          \n          <h3>¿Qué sucede después?</h3>\n          <ul>\n            <li>Tu acceso a las funciones premium finalizará el ${formattedDate}</li>\n            <li>Tu cuenta se convertirá automáticamente al Plan Gratuito</li>\n            <li>Conservarás acceso a las funciones básicas de OposI</li>\n            <li>Tus documentos y progreso se mantendrán guardados</li>\n          </ul>\n          \n          <h3>¿Cambiaste de opinión?</h3>\n          <p>Si deseas reactivar tu suscripción, puedes hacerlo en cualquier momento desde tu panel de control:</p>\n          <p style=\"text-align: center; margin: 20px 0;\">\n            <a href=\"${\"http://localhost:3000\"}/upgrade-plan\" \n               style=\"background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n              Reactivar Suscripción\n            </a>\n          </p>\n          \n          <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;\">\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br>\n            Equipo de OposI\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\nSuscripción Cancelada - OposI\n\nHola ${userName},\n\nHemos recibido tu solicitud de cancelación de la suscripción al Plan ${planName}.\n\nPERÍODO DE GRACIA ACTIVO:\n- Mantienes acceso completo hasta: ${formattedDate}\n- Días restantes: ${daysRemaining} días\n\n¿Qué sucede después?\n- Tu acceso a las funciones premium finalizará el ${formattedDate}\n- Tu cuenta se convertirá automáticamente al Plan Gratuito\n- Conservarás acceso a las funciones básicas de OposI\n- Tus documentos y progreso se mantendrán guardados\n\n¿Cambiaste de opinión?\nPuedes reactivar tu suscripción en cualquier momento desde: ${\"http://localhost:3000\"}/upgrade-plan\n\nSi tienes alguna pregunta, no dudes en contactarnos.\nEquipo de OposI\n    `;\n        const subject = `Suscripción cancelada - Acceso hasta el ${gracePeriodDate.toLocaleDateString('es-ES')}`;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n    /**\n   * Template para recordatorio de que el período de gracia está por terminar\n   */ static generateGracePeriodEndingEmail(userName, planName, gracePeriodEnd) {\n        const gracePeriodDate = new Date(gracePeriodEnd);\n        const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n        const hoursRemaining = Math.ceil((gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60));\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Tu acceso premium termina pronto - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #dc2626;\">⏰ Tu acceso premium termina pronto</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>Te recordamos que tu acceso al <strong>Plan ${planName}</strong> terminará el <strong>${formattedDate}</strong> (en aproximadamente ${hoursRemaining} horas).</p>\n          \n          <div style=\"background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;\">\n            <h3 style=\"margin-top: 0; color: #92400e;\">¿Quieres continuar con tu plan premium?</h3>\n            <p>Reactivar tu suscripción es fácil y rápido. Mantén acceso a todas las funciones avanzadas de OposI.</p>\n          </div>\n          \n          <p style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${\"http://localhost:3000\"}/upgrade-plan\" \n               style=\"background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;\">\n              Reactivar Mi Suscripción\n            </a>\n          </p>\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\nTu acceso premium termina pronto - OposI\n\nHola ${userName},\n\nTe recordamos que tu acceso al Plan ${planName} terminará el ${formattedDate} (en aproximadamente ${hoursRemaining} horas).\n\n¿Quieres continuar con tu plan premium?\nReactivar tu suscripción: ${\"http://localhost:3000\"}/upgrade-plan\n\nSi no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.\n\nEquipo de OposI\n    `;\n        const subject = `⏰ Tu Plan ${planName} termina en ${hoursRemaining} horas`;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n    /**\n   * Template base para otros tipos de notificaciones\n   */ static generateGenericEmail(userName, title, message, ctaText, ctaUrl) {\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>${title} - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #2563eb;\">${title}</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>${message}</p>\n          \n          ${ctaText && ctaUrl ? `\n          <p style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${ctaUrl}\" \n               style=\"background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n              ${ctaText}\n            </a>\n          </p>\n          ` : ''}\n          \n          <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;\">\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si tienes alguna pregunta, no dudes en contactarnos.<br>\n            Equipo de OposI\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\n${title} - OposI\n\nHola ${userName},\n\n${message}\n\n${ctaText && ctaUrl ? `${ctaText}: ${ctaUrl}` : ''}\n\nSi tienes alguna pregunta, no dudes en contactarnos.\nEquipo de OposI\n    `;\n        return {\n            htmlContent,\n            textContent,\n            subject: title\n        };\n    }\n    /**\n   * Template para confirmación de upgrade de plan (SEGURIDAD)\n   */ static generatePlanUpgradeConfirmation(userName, newPlanId, confirmationToken) {\n        const confirmationUrl = `${\"http://localhost:3000\"}/auth/confirm-upgrade?token=${confirmationToken}`;\n        const planDisplayName = newPlanId === 'usuario' ? 'Usuario' : newPlanId === 'pro' ? 'Pro' : newPlanId;\n        const subject = `⚠️ Acción Requerida: Confirma la actualización de tu plan en OposiAI`;\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Confirma la Actualización de tu Plan - OposiAI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <div style=\"text-align: center; margin-bottom: 30px;\">\n            <h1 style=\"color: #2563eb; margin-bottom: 10px;\">🔒 Confirma la Actualización de tu Plan</h1>\n          </div>\n\n          <div style=\"background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; border-radius: 4px;\">\n            <h3 style=\"margin-top: 0; color: #92400e;\">⚠️ Acción de Seguridad Requerida</h3>\n            <p style=\"margin-bottom: 0;\"><strong>Hemos detectado un intento de actualización de plan en tu cuenta.</strong></p>\n          </div>\n\n          <p>Hola <strong>${userName}</strong>,</p>\n\n          <p>Hemos recibido una solicitud para actualizar tu cuenta al <strong>Plan ${planDisplayName}</strong>. Esta solicitud se ha realizado mediante un pago asociado a tu dirección de email.</p>\n\n          <p><strong>Para proteger tu cuenta, necesitamos que confirmes que has sido tú quien ha realizado esta acción.</strong></p>\n\n          <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${confirmationUrl}\"\n               style=\"background-color: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;\">\n              ✅ Sí, actualizar mi plan\n            </a>\n          </div>\n\n          <div style=\"background-color: #fee2e2; border-left: 4px solid #dc2626; padding: 20px; margin: 20px 0; border-radius: 4px;\">\n            <h4 style=\"margin-top: 0; color: #991b1b;\">🚨 Si NO has solicitado esta actualización:</h4>\n            <ul style=\"margin-bottom: 0;\">\n              <li>Por favor, <strong>ignora este email</strong></li>\n              <li>Contacta inmediatamente con nuestro soporte</li>\n              <li>Considera cambiar tu contraseña por seguridad</li>\n            </ul>\n          </div>\n\n          <div style=\"background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n            <p style=\"margin: 0; font-size: 14px; color: #6b7280;\">\n              <strong>Información de seguridad:</strong><br>\n              • Este enlace es válido durante 24 horas<br>\n              • Solo tú puedes confirmar esta actualización<br>\n              • El pago se procesará únicamente tras tu confirmación\n            </p>\n          </div>\n\n          <hr style=\"border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;\">\n\n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si tienes alguna pregunta sobre esta solicitud, contacta con nuestro equipo de soporte.<br>\n            <strong>Equipo de OposiAI</strong>\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\n⚠️ ACCIÓN REQUERIDA: Confirma la actualización de tu plan en OposiAI\n\nHola ${userName},\n\n🔒 ACCIÓN DE SEGURIDAD REQUERIDA\n\nHemos recibido una solicitud para actualizar tu cuenta al Plan ${planDisplayName}. Esta solicitud se ha realizado mediante un pago asociado a tu dirección de email.\n\nPara proteger tu cuenta, necesitamos que confirmes que has sido tú quien ha realizado esta acción.\n\nCONFIRMAR ACTUALIZACIÓN:\n${confirmationUrl}\n\n🚨 SI NO HAS SOLICITADO ESTA ACTUALIZACIÓN:\n- Por favor, ignora este email\n- Contacta inmediatamente con nuestro soporte\n- Considera cambiar tu contraseña por seguridad\n\nINFORMACIÓN DE SEGURIDAD:\n• Este enlace es válido durante 24 horas\n• Solo tú puedes confirmar esta actualización\n• El pago se procesará únicamente tras tu confirmación\n\nSi tienes alguna pregunta sobre esta solicitud, contacta con nuestro equipo de soporte.\n\nEquipo de OposiAI\n    `;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/stripeWebhookHandlers.ts":
/*!***************************************************!*\
  !*** ./src/lib/services/stripeWebhookHandlers.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StripeWebhookHandlers: () => (/* binding */ StripeWebhookHandlers)\n/* harmony export */ });\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/stripe/config */ \"(rsc)/./src/lib/stripe/config.ts\");\n/* harmony import */ var _userManagement__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./userManagement */ \"(rsc)/./src/lib/services/userManagement.ts\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _email_emailNotificationService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./email/emailNotificationService */ \"(rsc)/./src/lib/services/email/emailNotificationService.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_4__);\n// src/lib/services/stripeWebhookHandlers.ts\n// Manejadores específicos para eventos de webhooks de Stripe\n\n\n\n\n\nclass StripeWebhookHandlers {\n    /**\n   * Manejar evento checkout.session.completed\n   * Se ejecuta cuando un pago se completa exitosamente\n   */ static async handleCheckoutSessionCompleted(session) {\n        try {\n            console.log('🎯 Procesando checkout.session.completed:', session.id);\n            // Validar que el pago esté completado\n            if (session.payment_status !== 'paid') {\n                return {\n                    success: false,\n                    message: 'Payment not completed',\n                    error: `Payment status: ${session.payment_status}`\n                };\n            }\n            // Verificar si es una compra de tokens\n            if (session.metadata?.type === 'token_purchase') {\n                return await this.handleTokenPurchase(session);\n            }\n            // Extraer metadata para creación de usuario\n            const { planId, customerEmail, customerName } = session.metadata || {};\n            if (!planId || !customerEmail) {\n                return {\n                    success: false,\n                    message: 'Missing required metadata',\n                    error: 'planId and customerEmail are required'\n                };\n            }\n            // Obtener el ID de la suscripción si el modo es 'subscription'\n            const subscriptionId = session.mode === 'subscription' ? session.subscription : undefined;\n            // Verificar si ya fue procesado\n            const existingTransaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.getTransactionBySessionId(session.id);\n            if (existingTransaction) {\n                console.log('⚠️ Transacción ya procesada:', existingTransaction.id);\n                return {\n                    success: true,\n                    message: 'Transaction already processed',\n                    data: {\n                        transactionId: existingTransaction.id\n                    }\n                };\n            }\n            // Verificar si ya existe un usuario con este customer ID (para evitar duplicados)\n            const { data: existingProfile } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan').eq('stripe_customer_id', session.customer).single();\n            if (existingProfile) {\n                console.log('⚠️ Usuario ya existe para este customer ID:', session.customer);\n                // Si es una suscripción nueva para un usuario existente, actualizar el plan\n                if (subscriptionId) {\n                    const result = await _userManagement__WEBPACK_IMPORTED_MODULE_1__.UserManagementService.updateUserPlan(existingProfile.user_id, planId, undefined, 'New subscription for existing customer');\n                    if (result.success) {\n                        return {\n                            success: true,\n                            message: 'Existing user plan updated with new subscription',\n                            data: {\n                                userId: existingProfile.user_id\n                            }\n                        };\n                    }\n                }\n                return {\n                    success: false,\n                    message: 'Customer already exists but plan update failed'\n                };\n            }\n            // Para suscripciones, obtener current_period_end directamente de Stripe\n            let planExpiresAt = null;\n            if (subscriptionId && _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__.stripe) {\n                try {\n                    const subscription = await _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__.stripe.subscriptions.retrieve(subscriptionId);\n                    planExpiresAt = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : null;\n                    console.log(`[handleCheckoutSessionCompleted] Plan expires at from subscription: ${planExpiresAt}`);\n                } catch (error) {\n                    console.error('[handleCheckoutSessionCompleted] Error obteniendo suscripción:', error);\n                }\n            }\n            // Verificar si hay datos de registro en los metadatos (flujo legacy)\n            const registrationData = session.metadata?.registrationData;\n            const preRegisteredUserId = session.metadata?.userId;\n            let result;\n            if (preRegisteredUserId) {\n                // NUEVO FLUJO: Usuario ya pre-registrado, activar y enviar email de confirmación\n                console.log(`🆕 Activando usuario pre-registrado después del pago: ${preRegisteredUserId}`);\n                try {\n                    // 1. Crear transacción de Stripe\n                    const transaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.createStripeTransaction({\n                        stripe_session_id: session.id,\n                        stripe_customer_id: session.customer,\n                        user_email: customerEmail,\n                        user_name: customerName,\n                        plan_id: planId,\n                        amount: session.amount_total || 0,\n                        currency: session.currency || 'eur',\n                        payment_status: 'paid',\n                        subscription_id: subscriptionId,\n                        user_id: preRegisteredUserId,\n                        metadata: {\n                            created_by: 'webhook',\n                            activation_flow: 'pre_registered_user'\n                        }\n                    });\n                    // 2. Actualizar perfil de usuario con datos de pago\n                    const { error: updateError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                        payment_verified: true,\n                        stripe_customer_id: session.customer,\n                        stripe_subscription_id: subscriptionId,\n                        last_payment_date: new Date().toISOString(),\n                        plan_expires_at: planExpiresAt,\n                        auto_renew: subscriptionId ? true : false,\n                        updated_at: new Date().toISOString(),\n                        security_flags: {\n                            payment_completed: true,\n                            payment_date: new Date().toISOString(),\n                            stripe_session_id: session.id,\n                            subscription_id: subscriptionId,\n                            pre_registered: false,\n                            activated: true\n                        }\n                    }).eq('user_id', preRegisteredUserId);\n                    if (updateError) {\n                        console.error('Error actualizando usuario pre-registrado:', updateError);\n                        result = {\n                            success: false,\n                            error: updateError.message\n                        };\n                    } else {\n                        // 3. Enviar email de confirmación AHORA que el pago está completado\n                        console.log('📧 Enviando email de confirmación después del pago exitoso...');\n                        const emailResult = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.sendConfirmationEmailForUser(preRegisteredUserId);\n                        if (!emailResult.success) {\n                            console.error('⚠️ Error enviando email de confirmación:', emailResult.error);\n                        // No fallar completamente, el usuario puede confirmar manualmente\n                        } else {\n                            console.log('✅ Email de confirmación enviado exitosamente después del pago');\n                        }\n                        // 5. Activar transacción\n                        await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.activateTransaction(transaction.id);\n                        result = {\n                            success: true,\n                            userId: preRegisteredUserId,\n                            transactionId: transaction.id,\n                            activated: true\n                        };\n                    }\n                } catch (activationError) {\n                    console.error('Error activando usuario pre-registrado:', activationError);\n                    result = {\n                        success: false,\n                        error: activationError instanceof Error ? activationError.message : 'Activation error'\n                    };\n                }\n            } else if (registrationData) {\n                // FLUJO LEGACY: Crear cuenta después del pago exitoso (mantener compatibilidad)\n                console.log(`🔄 Flujo legacy: Creando cuenta después del pago exitoso para: ${customerEmail}`);\n                try {\n                    const regData = JSON.parse(registrationData);\n                    // Crear usuario con plan usando los datos de registro\n                    result = await _userManagement__WEBPACK_IMPORTED_MODULE_1__.UserManagementService.createUserWithPlan({\n                        email: regData.email,\n                        password: regData.password,\n                        name: regData.customerName,\n                        planId: regData.planId,\n                        stripeSessionId: session.id,\n                        stripeCustomerId: session.customer,\n                        amount: session.amount_total || 0,\n                        currency: session.currency || 'eur',\n                        subscriptionId: subscriptionId,\n                        planExpiresAt: planExpiresAt,\n                        sendConfirmationEmail: true // ENVIAR EMAIL DESPUÉS DEL PAGO\n                    });\n                } catch (parseError) {\n                    console.error('Error parseando datos de registro:', parseError);\n                    result = {\n                        success: false,\n                        error: 'Invalid registration data format'\n                    };\n                }\n            } else {\n                // Flujo original: crear usuario con plan\n                result = await _userManagement__WEBPACK_IMPORTED_MODULE_1__.UserManagementService.createUserWithPlan({\n                    email: customerEmail,\n                    name: customerName,\n                    planId: planId,\n                    stripeSessionId: session.id,\n                    stripeCustomerId: session.customer,\n                    amount: session.amount_total || 0,\n                    currency: session.currency || 'eur',\n                    subscriptionId: subscriptionId,\n                    planExpiresAt: planExpiresAt // Pasar la fecha de expiración\n                });\n            }\n            if (!result.success) {\n                // Si el error es porque el email ya existe, intentar un flujo de actualización\n                const isEmailExistsError = result.error && (result.error.includes('A user with this email address has already been registered') || result.error.includes('email_exists') || result.error.includes('already been registered') || result.error.includes('User already registered'));\n                if (isEmailExistsError) {\n                    // ===== INICIO DE LA MODIFICACIÓN DE SEGURIDAD =====\n                    console.log(`📧 Email existente detectado. Iniciando flujo de confirmación para: ${customerEmail}`);\n                    console.log(`🔒 [SECURITY] Potential plan upgrade attempt for existing email: ${customerEmail}, planId: ${planId}, sessionId: ${session.id}`);\n                    try {\n                        // 1. Obtener el User ID del propietario de la cuenta\n                        const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.getUserByEmail(customerEmail);\n                        if (!existingUser) {\n                            // Esto no debería ocurrir si isEmailExistsError es verdadero, pero es una buena salvaguarda.\n                            console.error(`🚨 [SECURITY] Usuario existente no encontrado a pesar del error de duplicado: ${customerEmail}`);\n                            return {\n                                success: false,\n                                message: 'Failed to retrieve existing user by email.',\n                                error: 'User not found despite email_exists error'\n                            };\n                        }\n                        // 2. Crear un token de confirmación único y seguro.\n                        const confirmationToken = (0,crypto__WEBPACK_IMPORTED_MODULE_4__.randomUUID)();\n                        const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // Token expira en 24h\n                        console.log(`🔑 [SECURITY] Generated confirmation token for user ${existingUser.id}, expires at: ${expiresAt.toISOString()}`);\n                        // 3. Guardar el intento de actualización pendiente en la metadata de la transacción.\n                        const transaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.getTransactionBySessionId(session.id);\n                        if (transaction) {\n                            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('stripe_transactions').update({\n                                metadata: {\n                                    ...transaction.metadata,\n                                    pending_upgrade: {\n                                        userId: existingUser.id,\n                                        newPlanId: planId,\n                                        confirmationToken: confirmationToken,\n                                        tokenExpiresAt: expiresAt.toISOString(),\n                                        status: 'pending_confirmation',\n                                        createdAt: new Date().toISOString(),\n                                        customerEmail: customerEmail,\n                                        customerName: customerName,\n                                        stripeCustomerId: session.customer,\n                                        stripeSubscriptionId: subscriptionId\n                                    }\n                                }\n                            }).eq('id', transaction.id);\n                            console.log(`💾 [SECURITY] Saved pending upgrade data for transaction: ${transaction.id}`);\n                        }\n                        // 4. Enviar email de confirmación al propietario de la cuenta (Usuario B)\n                        const emailSent = await _email_emailNotificationService__WEBPACK_IMPORTED_MODULE_3__.EmailNotificationService.sendPlanUpgradeConfirmationEmail(customerEmail, existingUser.user_metadata?.name || customerName || 'Usuario', planId, confirmationToken);\n                        if (!emailSent) {\n                            console.error(`📧 [SECURITY] Failed to send confirmation email to: ${customerEmail}`);\n                        // No fallar el webhook por esto, pero registrar el error\n                        } else {\n                            console.log(`✅ [SECURITY] Confirmation email sent successfully to: ${customerEmail}`);\n                        }\n                        // 5. Retornar una respuesta de éxito al webhook, indicando que el proceso está pendiente.\n                        return {\n                            success: true,\n                            message: 'Existing user detected. Confirmation email sent for plan upgrade.',\n                            data: {\n                                status: 'pending_confirmation',\n                                userId: existingUser.id,\n                                confirmationRequired: true,\n                                emailSent: emailSent\n                            }\n                        };\n                    } catch (confirmationError) {\n                        console.error('❌ [SECURITY] Error en el flujo de confirmación de upgrade:', confirmationError);\n                        return {\n                            success: false,\n                            message: 'Failed to process plan upgrade confirmation flow.',\n                            error: confirmationError instanceof Error ? confirmationError.message : 'Unknown confirmation error'\n                        };\n                    }\n                // ===== FIN DE LA MODIFICACIÓN DE SEGURIDAD =====\n                }\n                // Si es otro tipo de error\n                return {\n                    success: false,\n                    message: 'Failed to create user',\n                    error: result.error\n                };\n            }\n            console.log('✅ Usuario creado exitosamente desde webhook');\n            return {\n                success: true,\n                message: 'User created successfully',\n                data: {\n                    userId: result.userId,\n                    profileId: result.profileId,\n                    transactionId: result.transactionId\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleCheckoutSessionCompleted:', error);\n            return {\n                success: false,\n                message: 'Internal error processing checkout session',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar compra de tokens adicionales\n   */ static async handleTokenPurchase(session) {\n        try {\n            console.log('🪙 Procesando compra de tokens:', session.id);\n            const { user_id, token_amount, price } = session.metadata || {};\n            if (!user_id || !token_amount || !price) {\n                return {\n                    success: false,\n                    message: 'Missing required metadata for token purchase',\n                    error: 'user_id, token_amount, and price are required'\n                };\n            }\n            // Verificar si ya fue procesado\n            const { data: existingPurchase } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('token_purchases').select('id').eq('transaction_id', session.id).single();\n            if (existingPurchase) {\n                console.log('⚠️ Compra de tokens ya procesada:', existingPurchase.id);\n                return {\n                    success: true,\n                    message: 'Token purchase already processed',\n                    data: {\n                        purchaseId: existingPurchase.id\n                    }\n                };\n            }\n            const tokenAmount = parseInt(token_amount);\n            const purchasePrice = parseFloat(price);\n            // Registrar la compra de tokens\n            const { data: purchase, error: purchaseError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('token_purchases').insert([\n                {\n                    user_id: user_id,\n                    amount: tokenAmount,\n                    price: purchasePrice,\n                    transaction_id: session.id,\n                    status: 'completed'\n                }\n            ]).select().single();\n            if (purchaseError) {\n                console.error('Error registrando compra de tokens:', purchaseError);\n                return {\n                    success: false,\n                    message: 'Error registering token purchase',\n                    error: purchaseError.message\n                };\n            }\n            // Actualizar límite de tokens del usuario\n            const { data: profile, error: profileError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('monthly_token_limit').eq('user_id', user_id).single();\n            if (profileError || !profile) {\n                console.error('Error obteniendo perfil de usuario:', profileError);\n                return {\n                    success: false,\n                    message: 'User profile not found',\n                    error: profileError?.message || 'Profile not found'\n                };\n            }\n            const newTokenLimit = profile.monthly_token_limit + tokenAmount;\n            const { error: updateError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                monthly_token_limit: newTokenLimit,\n                updated_at: new Date().toISOString()\n            }).eq('user_id', user_id);\n            if (updateError) {\n                console.error('Error actualizando límite de tokens:', updateError);\n                return {\n                    success: false,\n                    message: 'Error updating token limit',\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Compra de tokens procesada exitosamente:', {\n                userId: user_id,\n                tokensAdded: tokenAmount,\n                newLimit: newTokenLimit,\n                purchaseId: purchase.id\n            });\n            return {\n                success: true,\n                message: 'Token purchase processed successfully',\n                data: {\n                    purchaseId: purchase.id,\n                    tokensAdded: tokenAmount,\n                    newTokenLimit: newTokenLimit\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleTokenPurchase:', error);\n            return {\n                success: false,\n                message: 'Internal error processing token purchase',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento payment_intent.succeeded\n   * Para pagos únicos exitosos\n   */ static async handlePaymentIntentSucceeded(paymentIntent) {\n        try {\n            console.log('💳 Procesando payment_intent.succeeded:', paymentIntent.id);\n            // Para pagos únicos, la lógica principal está en checkout.session.completed\n            // Aquí solo registramos el evento para auditoría\n            return {\n                success: true,\n                message: 'Payment intent logged successfully',\n                data: {\n                    paymentIntentId: paymentIntent.id\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handlePaymentIntentSucceeded:', error);\n            return {\n                success: false,\n                message: 'Error processing payment intent',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento customer.subscription.created\n   * Cuando se crea una nueva suscripción\n   */ static async handleSubscriptionCreated(subscription) {\n        try {\n            console.log('🔄 Procesando customer.subscription.created:', subscription.id);\n            // Obtener información del cliente\n            const customerId = subscription.customer;\n            const planId = subscription.metadata?.planId;\n            if (!planId) {\n                return {\n                    success: false,\n                    message: 'Missing plan ID in subscription metadata'\n                };\n            }\n            // Actualizar información de suscripción en la transacción\n            const { error: transactionError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('stripe_transactions').update({\n                subscription_id: subscription.id,\n                metadata: {\n                    subscription_status: subscription.status,\n                    subscription_id: subscription.id,\n                    updated_at: new Date().toISOString()\n                }\n            }).eq('stripe_customer_id', customerId);\n            if (transactionError) {\n                console.error('Error actualizando transacción con suscripción:', transactionError);\n            }\n            // Establecer plan_expires_at inicial usando current_period_end de la suscripción\n            const planExpiresAt = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // Fallback a 30 días\n            console.log(`[handleSubscriptionCreated] Actualizando perfil para customerId: ${customerId}`);\n            console.log(`[handleSubscriptionCreated] stripe_subscription_id: ${subscription.id}, plan_expires_at: ${planExpiresAt}`);\n            // Intentar actualizar por stripe_customer_id primero\n            let { error: profileError, data: updatedProfileData } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                stripe_subscription_id: subscription.id,\n                plan_expires_at: planExpiresAt,\n                auto_renew: true,\n                last_payment_date: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    subscription_status: subscription.status,\n                    subscription_id: subscription.id,\n                    subscription_created_at: new Date().toISOString()\n                }\n            }).eq('stripe_customer_id', customerId).select();\n            // Si no se encontró por stripe_customer_id, buscar por email en los metadatos\n            if (!updatedProfileData || updatedProfileData.length === 0) {\n                console.log(`[handleSubscriptionCreated] No se encontró perfil por customerId, buscando por email...`);\n                const customerEmail = subscription.metadata?.customerEmail;\n                if (customerEmail) {\n                    // Obtener user_id por email\n                    const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.getUserByEmail(customerEmail);\n                    if (existingUser) {\n                        console.log(`[handleSubscriptionCreated] Actualizando perfil por user_id: ${existingUser.id}`);\n                        const updateResult = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                            stripe_customer_id: customerId,\n                            stripe_subscription_id: subscription.id,\n                            plan_expires_at: planExpiresAt,\n                            auto_renew: true,\n                            last_payment_date: new Date().toISOString(),\n                            updated_at: new Date().toISOString(),\n                            security_flags: {\n                                subscription_status: subscription.status,\n                                subscription_id: subscription.id,\n                                subscription_created_at: new Date().toISOString()\n                            }\n                        }).eq('user_id', existingUser.id).select();\n                        profileError = updateResult.error;\n                        updatedProfileData = updateResult.data;\n                    }\n                }\n            }\n            if (profileError) {\n                console.error('[handleSubscriptionCreated] Error actualizando perfil con suscripción:', profileError);\n            } else {\n                console.log('[handleSubscriptionCreated] Perfil actualizado con datos de suscripción:', updatedProfileData);\n            }\n            return {\n                success: true,\n                message: 'Subscription created successfully',\n                data: {\n                    subscriptionId: subscription.id,\n                    planExpiresAt\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleSubscriptionCreated:', error);\n            return {\n                success: false,\n                message: 'Error processing subscription creation',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento customer.subscription.updated\n   * Cuando se actualiza una suscripción\n   */ static async handleSubscriptionUpdated(subscription) {\n        try {\n            console.log('🔄 Procesando customer.subscription.updated:', subscription.id);\n            // Obtener usuario por customer ID\n            const { data: profile } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan').eq('stripe_customer_id', subscription.customer).single();\n            if (!profile) {\n                return {\n                    success: false,\n                    message: 'User profile not found for customer'\n                };\n            }\n            // Actualizar estado de la suscripción usando current_period_end de Stripe\n            const planExpiresAt = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // Fallback a 30 días\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                plan_expires_at: planExpiresAt,\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    subscription_status: subscription.status,\n                    subscription_id: subscription.id,\n                    last_updated: new Date().toISOString()\n                }\n            }).eq('user_id', profile.user_id);\n            if (error) {\n                console.error('Error actualizando perfil de usuario:', error);\n                return {\n                    success: false,\n                    message: 'Error updating user profile'\n                };\n            }\n            return {\n                success: true,\n                message: 'Subscription updated successfully',\n                data: {\n                    subscriptionId: subscription.id\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleSubscriptionUpdated:', error);\n            return {\n                success: false,\n                message: 'Error processing subscription update',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento customer.subscription.deleted\n   * Cuando se cancela una suscripción - implementa período de gracia\n   */ static async handleSubscriptionDeleted(subscription) {\n        try {\n            console.log('❌ Procesando customer.subscription.deleted:', subscription.id);\n            // Obtener usuario por customer ID\n            const { data: profile } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan, plan_expires_at').eq('stripe_customer_id', subscription.customer).single();\n            if (!profile) {\n                return {\n                    success: false,\n                    message: 'User profile not found for customer'\n                };\n            }\n            // Obtener current_period_end de la suscripción para período de gracia\n            const gracePeriodEnd = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date().toISOString(); // Si no hay current_period_end, expirar inmediatamente\n            console.log(`🕐 Período de gracia hasta: ${gracePeriodEnd} para usuario: ${profile.user_id}`);\n            // En lugar de degradar inmediatamente, mantener plan actual hasta current_period_end\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                plan_expires_at: gracePeriodEnd,\n                auto_renew: false,\n                stripe_subscription_id: null,\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    subscription_cancelled: true,\n                    cancellation_date: new Date().toISOString(),\n                    grace_period_until: gracePeriodEnd,\n                    cancelled_subscription_id: subscription.id,\n                    last_updated: new Date().toISOString()\n                }\n            }).eq('user_id', profile.user_id);\n            if (error) {\n                console.error('Error actualizando perfil para período de gracia:', error);\n                return {\n                    success: false,\n                    message: 'Error setting up grace period',\n                    error: error.message\n                };\n            }\n            // Registrar el cambio en el historial\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.logPlanChange({\n                user_id: profile.user_id,\n                old_plan: profile.subscription_plan,\n                new_plan: profile.subscription_plan,\n                changed_by: 'system',\n                reason: `Subscription cancelled - Grace period until ${gracePeriodEnd}`\n            });\n            // Obtener información del usuario para enviar notificación por email\n            try {\n                const { data: userData } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.auth.admin.getUserById(profile.user_id);\n                if (userData.user?.email) {\n                    const userName = userData.user.user_metadata?.name || userData.user.email.split('@')[0];\n                    const planName = profile.subscription_plan === 'usuario' ? 'Usuario' : 'Pro';\n                    // Enviar notificación de cancelación con período de gracia\n                    await _email_emailNotificationService__WEBPACK_IMPORTED_MODULE_3__.EmailNotificationService.sendSubscriptionCancelledNotification(userData.user.email, userName, planName, gracePeriodEnd, profile.user_id);\n                    console.log(`📧 Notificación de cancelación enviada a: ${userData.user.email}`);\n                }\n            } catch (emailError) {\n                console.error('Error enviando notificación de cancelación:', emailError);\n            // No fallar el webhook por error de email\n            }\n            return {\n                success: true,\n                message: `Subscription cancelled with grace period until ${gracePeriodEnd}`,\n                data: {\n                    userId: profile.user_id,\n                    gracePeriodEnd,\n                    currentPlan: profile.subscription_plan\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleSubscriptionDeleted:', error);\n            return {\n                success: false,\n                message: 'Error processing subscription deletion',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento invoice.payment_succeeded\n   * Para renovaciones de suscripción\n   */ static async handleInvoicePaymentSucceeded(invoice) {\n        try {\n            console.log('💰 Procesando invoice.payment_succeeded:', invoice.id);\n            // Verificar si la factura está relacionada con una suscripción\n            const subscriptionId = invoice.subscription;\n            if (!subscriptionId) {\n                return {\n                    success: true,\n                    message: 'Invoice not related to subscription, skipping'\n                };\n            }\n            // Obtener información de la suscripción para actualizar plan_expires_at\n            let planExpiresAt = null;\n            try {\n                if (subscriptionId && _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__.stripe) {\n                    const subscription = await _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__.stripe.subscriptions.retrieve(subscriptionId);\n                    planExpiresAt = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : null;\n                }\n            } catch (error) {\n                console.error('Error obteniendo información de suscripción:', error);\n            }\n            // Obtener perfil del usuario para determinar si es renovación\n            const { data: profile } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan, current_month_tokens, monthly_token_limit').eq('stripe_customer_id', invoice.customer).single();\n            // Actualizar fecha de último pago y plan_expires_at\n            const updateData = {\n                last_payment_date: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            };\n            if (planExpiresAt) {\n                updateData.plan_expires_at = planExpiresAt;\n                // Para suscripciones, resetear tokens al inicio del nuevo período de facturación\n                if (profile && (profile.subscription_plan === 'usuario' || profile.subscription_plan === 'pro')) {\n                    updateData.current_month_tokens = 0;\n                    updateData.current_month = new Date().toISOString().slice(0, 7) + '-01';\n                    console.log('🔄 Reseteando tokens para renovación de suscripción:', profile.user_id);\n                }\n            }\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update(updateData).eq('stripe_customer_id', invoice.customer);\n            if (error) {\n                console.error('Error actualizando fecha de pago:', error);\n            }\n            return {\n                success: true,\n                message: 'Invoice payment processed successfully',\n                data: {\n                    invoiceId: invoice.id\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleInvoicePaymentSucceeded:', error);\n            return {\n                success: false,\n                message: 'Error processing invoice payment',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/stripeWebhookHandlers.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/userManagement.ts":
/*!********************************************!*\
  !*** ./src/lib/services/userManagement.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserManagementService: () => (/* binding */ UserManagementService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/plans */ \"(rsc)/./src/config/plans.ts\");\n// src/lib/services/userManagement.ts\n// Servicio para gestión automatizada de usuarios y perfiles\n\n\nclass UserManagementService {\n    /**\n   * Crear usuario completo con perfil y transacción\n   */ static async createUserWithPlan(request) {\n        try {\n            console.log('🚀 Iniciando creación de usuario:', request.email);\n            // 1. Validar plan\n            const planConfig = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(request.planId);\n            if (!planConfig) {\n                throw new Error(`Plan inválido: ${request.planId}`);\n            }\n            // 2. Verificar si ya existe una transacción para esta sesión\n            const existingTransaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getTransactionBySessionId(request.stripeSessionId);\n            if (existingTransaction) {\n                console.log('⚠️ Transacción ya existe:', existingTransaction.id);\n                return {\n                    success: false,\n                    error: 'Transacción ya procesada'\n                };\n            }\n            // 3. Crear registro de transacción\n            const transaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.createStripeTransaction({\n                stripe_session_id: request.stripeSessionId,\n                stripe_customer_id: request.stripeCustomerId,\n                user_email: request.email,\n                user_name: request.name,\n                plan_id: request.planId,\n                amount: request.amount,\n                currency: request.currency,\n                payment_status: 'paid',\n                subscription_id: request.subscriptionId,\n                metadata: {\n                    created_by: 'webhook',\n                    plan_name: planConfig.name\n                }\n            });\n            console.log('✅ Transacción creada:', transaction.id);\n            // 4. Crear usuario con contraseña específica o invitación\n            let createdUserId;\n            let userInvitation = null;\n            if (request.password && request.sendConfirmationEmail) {\n                // NUEVO FLUJO: Crear cuenta con contraseña específica y enviar email de confirmación\n                console.log('🆕 Creando cuenta con contraseña específica y email de confirmación');\n                try {\n                    userInvitation = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.createUserWithPassword(request.email, request.password, {\n                        name: request.name,\n                        plan: request.planId,\n                        stripe_session_id: request.stripeSessionId,\n                        stripe_customer_id: request.stripeCustomerId,\n                        transaction_id: transaction.id,\n                        payment_verified: true\n                    });\n                    createdUserId = userInvitation.user.id;\n                    console.log('✅ Usuario creado con contraseña y email de confirmación:', createdUserId);\n                } catch (passwordError) {\n                    console.error('Error creando usuario con contraseña:', passwordError);\n                    throw passwordError;\n                }\n            } else {\n                // FLUJO ANTERIOR: Crear invitación de usuario\n                const userData = {\n                    name: request.name,\n                    plan: request.planId,\n                    stripe_session_id: request.stripeSessionId,\n                    stripe_customer_id: request.stripeCustomerId,\n                    transaction_id: transaction.id,\n                    payment_verified: true\n                };\n                try {\n                    userInvitation = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.createUserWithInvitation(request.email, userData);\n                    createdUserId = userInvitation.user.id;\n                    console.log('✅ Invitación de usuario creada:', createdUserId);\n                } catch (invitationError) {\n                    // Detectar diferentes variaciones del error de email existente\n                    const isEmailExistsError = invitationError.message && (invitationError.message.includes('A user with this email address has already been registered') || invitationError.message.includes('email_exists') || invitationError.message.includes('already been registered') || invitationError.message.includes('User already registered'));\n                    if (isEmailExistsError) {\n                        console.log('⚠️ Usuario ya existe, actualizando plan existente');\n                        // Obtener el usuario existente de Supabase Auth\n                        const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserByEmail(request.email);\n                        if (!existingUser) {\n                            throw new Error('Error obteniendo usuario existente.');\n                        }\n                        createdUserId = existingUser.id;\n                    } else {\n                        // Otro error durante la invitación\n                        throw invitationError;\n                    }\n                }\n            }\n            // 5. Crear perfil de usuario y registrar historial de forma atómica\n            const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n            const isSubscription = !!request.subscriptionId;\n            // Verificar si ya existe un perfil para este usuario (para los security_flags)\n            const existingProfile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(createdUserId);\n            const profileData = {\n                subscription_plan: request.planId,\n                monthly_token_limit: (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(request.planId),\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: true,\n                stripe_customer_id: request.stripeCustomerId,\n                stripe_subscription_id: request.subscriptionId,\n                last_payment_date: new Date().toISOString(),\n                auto_renew: isSubscription,\n                plan_expires_at: request.planExpiresAt || (isSubscription ? null : this.calculatePlanExpiration(request.planId)),\n                plan_features: planConfig.features,\n                security_flags: {\n                    created_via_webhook: true,\n                    payment_method: 'stripe',\n                    activation_date: new Date().toISOString(),\n                    subscription_type: isSubscription ? 'recurring' : 'one_time',\n                    ...existingProfile?.security_flags || {}\n                }\n            };\n            const { data: creationResult, error: rpcError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc('create_user_profile_and_history', {\n                p_user_id: createdUserId,\n                p_transaction_id: transaction.id,\n                p_profile_data: profileData\n            }).single(); // .single() es importante para obtener un único resultado\n            if (rpcError) {\n                console.error('Error al ejecutar la función create_user_profile_and_history:', rpcError);\n                throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);\n            }\n            const profileId = creationResult.created_profile_id;\n            console.log('✅ Perfil y historial creados atómicamente. Profile ID:', profileId);\n            // 6. Actualizar transacción con user_id y marcar como activada\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.updateTransactionWithUser(transaction.id, createdUserId);\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.activateTransaction(transaction.id);\n            console.log('✅ Usuario procesado exitosamente:', createdUserId);\n            return {\n                success: true,\n                userId: createdUserId,\n                profileId: profileId,\n                transactionId: transaction.id\n            };\n        } catch (error) {\n            console.error('❌ Error creando usuario:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    /**\n   * Actualizar plan de usuario existente\n   */ static async updateUserPlan(userId, newPlanId, transactionId, reason = 'Plan upgrade/downgrade') {\n        try {\n            console.log('🔄 Actualizando plan de usuario:', userId, 'a', newPlanId);\n            // 1. Obtener perfil actual\n            const currentProfile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!currentProfile) {\n                throw new Error('Usuario no encontrado');\n            }\n            // 2. Validar nuevo plan\n            const planConfig = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(newPlanId);\n            if (!planConfig) {\n                throw new Error(`Plan inválido: ${newPlanId}`);\n            }\n            // 3. Actualizar perfil\n            const updatedProfile = {\n                ...currentProfile,\n                subscription_plan: newPlanId,\n                monthly_token_limit: (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(newPlanId),\n                last_payment_date: new Date().toISOString(),\n                payment_verified: true,\n                plan_features: planConfig.features,\n                updated_at: new Date().toISOString()\n            };\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.upsertUserProfile(updatedProfile);\n            // 4. Registrar cambio de plan\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.logPlanChange({\n                user_id: userId,\n                old_plan: currentProfile.subscription_plan,\n                new_plan: newPlanId,\n                changed_by: 'system',\n                reason,\n                transaction_id: transactionId\n            });\n            console.log('✅ Plan actualizado exitosamente');\n            return {\n                success: true,\n                userId,\n                profileId: profile.id\n            };\n        } catch (error) {\n            console.error('❌ Error actualizando plan:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    /**\n   * Verificar estado de pago de usuario\n   */ static async verifyUserPaymentStatus(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile) {\n                return {\n                    verified: false,\n                    plan: 'none'\n                };\n            }\n            return {\n                verified: profile.payment_verified,\n                plan: profile.subscription_plan,\n                expiresAt: profile.plan_expires_at || undefined,\n                lastPayment: profile.last_payment_date || undefined\n            };\n        } catch (error) {\n            console.error('Error verificando estado de pago:', error);\n            return {\n                verified: false,\n                plan: 'error'\n            };\n        }\n    }\n    /**\n   * Obtener estadísticas de usuarios\n   */ static async getUserStats() {\n        try {\n            // Esta función requeriría consultas más complejas\n            // Por ahora retornamos estructura básica\n            return {\n                total: 0,\n                byPlan: {\n                    free: 0,\n                    usuario: 0,\n                    pro: 0\n                },\n                verified: 0,\n                unverified: 0\n            };\n        } catch (error) {\n            console.error('Error obteniendo estadísticas:', error);\n            throw error;\n        }\n    }\n    /**\n   * Calcular fecha de expiración para pagos únicos y períodos de gracia\n   */ static calculatePlanExpiration(planId, isGracePeriod = false) {\n        const now = new Date();\n        switch(planId){\n            case 'free':\n                // Plan gratuito expira en 5 días\n                now.setDate(now.getDate() + 5);\n                break;\n            case 'usuario':\n                if (isGracePeriod) {\n                    // Período de gracia de 7 días después de cancelar suscripción\n                    now.setDate(now.getDate() + 7);\n                } else {\n                    // Pago único legacy: 30 días\n                    now.setDate(now.getDate() + 30);\n                }\n                break;\n            case 'pro':\n                if (isGracePeriod) {\n                    // Período de gracia de 14 días después de cancelar suscripción\n                    now.setDate(now.getDate() + 14);\n                } else {\n                    // Pago único legacy: 30 días\n                    now.setDate(now.getDate() + 30);\n                }\n                break;\n            default:\n                // Por defecto, 30 días\n                now.setDate(now.getDate() + 30);\n                break;\n        }\n        return now.toISOString();\n    }\n    /**\n   * Manejar cancelación de suscripción con período de gracia\n   */ static async handleSubscriptionCancellation(userId, currentPlan, subscriptionEndDate, reason = 'Subscription cancelled') {\n        try {\n            console.log('🔄 Manejando cancelación de suscripción:', userId, 'plan:', currentPlan);\n            // Obtener perfil actual\n            const currentProfile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!currentProfile) {\n                throw new Error('Usuario no encontrado');\n            }\n            // Determinar si dar período de gracia o pasar inmediatamente a free\n            let newPlan = 'free';\n            let planExpiresAt;\n            if (subscriptionEndDate) {\n                // Si tenemos la fecha de fin de suscripción de Stripe, usar esa fecha\n                planExpiresAt = subscriptionEndDate;\n                // Mantener el plan actual hasta que expire\n                newPlan = currentPlan;\n            } else {\n                // Sin fecha de fin, dar período de gracia basado en el plan\n                if (currentPlan === 'usuario' || currentPlan === 'pro') {\n                    planExpiresAt = this.calculatePlanExpiration(currentPlan, true);\n                    newPlan = currentPlan; // Mantener plan durante período de gracia\n                } else {\n                    // Para plan free, expirar inmediatamente\n                    planExpiresAt = new Date().toISOString();\n                    newPlan = 'free';\n                }\n            }\n            // Actualizar perfil\n            const updatedProfile = {\n                ...currentProfile,\n                subscription_plan: newPlan,\n                plan_expires_at: planExpiresAt,\n                auto_renew: false,\n                stripe_subscription_id: undefined,\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    ...currentProfile.security_flags,\n                    subscription_cancelled: true,\n                    cancellation_date: new Date().toISOString(),\n                    grace_period_until: planExpiresAt\n                }\n            };\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.upsertUserProfile(updatedProfile);\n            // Registrar cambio de plan\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.logPlanChange({\n                user_id: userId,\n                old_plan: currentProfile.subscription_plan,\n                new_plan: newPlan,\n                changed_by: 'system',\n                reason: `${reason} - Grace period until ${planExpiresAt}`\n            });\n            console.log('✅ Cancelación de suscripción procesada con período de gracia');\n            return {\n                success: true,\n                userId,\n                profileId: profile.id\n            };\n        } catch (error) {\n            console.error('❌ Error manejando cancelación de suscripción:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    /**\n   * Procesar usuarios cuyo período de gracia ha expirado\n   * Esta función debe ejecutarse periódicamente (ej: cron job diario)\n   */ static async processExpiredGracePeriods() {\n        try {\n            console.log('🔍 Buscando usuarios con período de gracia expirado...');\n            const now = new Date().toISOString();\n            // Buscar usuarios con período de gracia expirado\n            const { data: expiredUsers, error: searchError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan, plan_expires_at, security_flags').lt('plan_expires_at', now) // plan_expires_at < now\n            .eq('auto_renew', false) // No auto-renovable (cancelado)\n            .neq('subscription_plan', 'free') // No es plan gratuito\n            .limit(100); // Procesar máximo 100 por vez\n            if (searchError) {\n                throw new Error(`Error buscando usuarios expirados: ${searchError.message}`);\n            }\n            if (!expiredUsers || expiredUsers.length === 0) {\n                console.log('✅ No se encontraron usuarios con período de gracia expirado');\n                return {\n                    processed: 0,\n                    errors: []\n                };\n            }\n            console.log(`📋 Encontrados ${expiredUsers.length} usuarios con período de gracia expirado`);\n            const errors = [];\n            let processed = 0;\n            // Procesar cada usuario expirado\n            for (const user of expiredUsers){\n                try {\n                    // Verificar si realmente está en período de gracia\n                    const isInGracePeriod = user.security_flags?.subscription_cancelled === true;\n                    if (!isInGracePeriod) {\n                        console.log(`⚠️ Usuario ${user.user_id} expirado pero no en período de gracia, omitiendo`);\n                        continue;\n                    }\n                    console.log(`⬇️ Degradando usuario ${user.user_id} de ${user.subscription_plan} a free`);\n                    // Degradar a plan gratuito\n                    const result = await this.updateUserPlan(user.user_id, 'free', undefined, `Grace period expired - was ${user.subscription_plan}`);\n                    if (result.success) {\n                        processed++;\n                        console.log(`✅ Usuario ${user.user_id} degradado exitosamente`);\n                    } else {\n                        const errorMsg = `Error degradando usuario ${user.user_id}: ${result.error}`;\n                        console.error(errorMsg);\n                        errors.push(errorMsg);\n                    }\n                } catch (userError) {\n                    const errorMsg = `Error procesando usuario ${user.user_id}: ${userError instanceof Error ? userError.message : 'Unknown error'}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                }\n            }\n            console.log(`🎯 Procesamiento completado: ${processed} usuarios degradados, ${errors.length} errores`);\n            return {\n                processed,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en processExpiredGracePeriods:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/userManagement.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe/config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe/config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.APP_URLS),\n/* harmony export */   PLANS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.PLANS),\n/* harmony export */   getPlanById: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.getPlanById),\n/* harmony export */   isValidPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.isValidPlan),\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plans */ \"(rsc)/./src/lib/stripe/plans.ts\");\n// src/lib/stripe/config.ts\n\n// Inicializar Stripe solo en el servidor\nconst stripe =  true ? new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: '2025-05-28.basil',\n    typescript: true\n}) : 0;\n// Importar configuración de planes\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS9jb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLDJCQUEyQjtBQUNDO0FBRTVCLHlDQUF5QztBQUNsQyxNQUFNQyxTQUFTLEtBQTZCLEdBQy9DLElBQUlELDhDQUFNQSxDQUFDRSxRQUFRQyxHQUFHLENBQUNDLGlCQUFpQixFQUFHO0lBQ3pDQyxZQUFZO0lBQ1pDLFlBQVk7QUFDZCxLQUNBLENBQUksQ0FBQztBQUVULG1DQUFtQztBQUNpQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxzcmNcXGxpYlxcc3RyaXBlXFxjb25maWcudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2xpYi9zdHJpcGUvY29uZmlnLnRzXG5pbXBvcnQgU3RyaXBlIGZyb20gJ3N0cmlwZSc7XG5cbi8vIEluaWNpYWxpemFyIFN0cmlwZSBzb2xvIGVuIGVsIHNlcnZpZG9yXG5leHBvcnQgY29uc3Qgc3RyaXBlID0gdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCdcbiAgPyBuZXcgU3RyaXBlKHByb2Nlc3MuZW52LlNUUklQRV9TRUNSRVRfS0VZISwge1xuICAgICAgYXBpVmVyc2lvbjogJzIwMjUtMDUtMjguYmFzaWwnLFxuICAgICAgdHlwZXNjcmlwdDogdHJ1ZSxcbiAgICB9KVxuICA6IG51bGw7XG5cbi8vIEltcG9ydGFyIGNvbmZpZ3VyYWNpw7NuIGRlIHBsYW5lc1xuZXhwb3J0IHsgUExBTlMsIGdldFBsYW5CeUlkLCBpc1ZhbGlkUGxhbiwgQVBQX1VSTFMgfSBmcm9tICcuL3BsYW5zJztcbmV4cG9ydCB0eXBlIHsgUGxhbklkIH0gZnJvbSAnLi9wbGFucyc7XG4iXSwibmFtZXMiOlsiU3RyaXBlIiwic3RyaXBlIiwicHJvY2VzcyIsImVudiIsIlNUUklQRV9TRUNSRVRfS0VZIiwiYXBpVmVyc2lvbiIsInR5cGVzY3JpcHQiLCJQTEFOUyIsImdldFBsYW5CeUlkIiwiaXNWYWxpZFBsYW4iLCJBUFBfVVJMUyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config */ \"(rsc)/./src/config/index.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return plan?.planConfig || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        stripePriceId: 'price_tokens_additional'\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: `${\"http://localhost:3000\"}/thank-you`,\n    cancel: `${\"http://localhost:3000\"}/upgrade-plan`,\n    webhook: `${\"http://localhost:3000\"}/api/stripe/webhook`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe/plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: {\n                    ...user.user_metadata,\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                }\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/stripe","vendor-chunks/qs","vendor-chunks/object-inspect","vendor-chunks/get-intrinsic","vendor-chunks/side-channel-list","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/function-bind","vendor-chunks/side-channel-map","vendor-chunks/side-channel","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fwebhook%2Froute&page=%2Fapi%2Fstripe%2Fwebhook%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fwebhook%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();