"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_supabase_admin_ts"],{

/***/ "(app-pages-browser)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(\"Failed to create transaction: \".concat(error.message));\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(\"Failed to fetch transaction: \".concat(error.message));\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        var _data_user, _data_user1, _data_user2, _data_user3, _data_user4, _data_user5, _data_user6;\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: \"\".concat(\"http://localhost:3000\", \"/auth/callback\"),\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: \"\".concat(\"http://localhost:3000\", \"/auth/callback\")\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!(data === null || data === void 0 ? void 0 : data.user),\n            userId: data === null || data === void 0 ? void 0 : (_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id,\n            userEmail: data === null || data === void 0 ? void 0 : (_data_user1 = data.user) === null || _data_user1 === void 0 ? void 0 : _data_user1.email,\n            userAud: data === null || data === void 0 ? void 0 : (_data_user2 = data.user) === null || _data_user2 === void 0 ? void 0 : _data_user2.aud,\n            userRole: data === null || data === void 0 ? void 0 : (_data_user3 = data.user) === null || _data_user3 === void 0 ? void 0 : _data_user3.role,\n            emailConfirmed: data === null || data === void 0 ? void 0 : (_data_user4 = data.user) === null || _data_user4 === void 0 ? void 0 : _data_user4.email_confirmed_at,\n            userMetadata: data === null || data === void 0 ? void 0 : (_data_user5 = data.user) === null || _data_user5 === void 0 ? void 0 : _data_user5.user_metadata,\n            appMetadata: data === null || data === void 0 ? void 0 : (_data_user6 = data.user) === null || _data_user6 === void 0 ? void 0 : _data_user6.app_metadata,\n            error: error === null || error === void 0 ? void 0 : error.message,\n            errorCode: error === null || error === void 0 ? void 0 : error.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(\"Failed to create user invitation: \".concat(error.message));\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData) {\n        let sendConfirmationEmail = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : true;\n        var _data_user, _data_user1, _data_user2, _data_user3;\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!(data === null || data === void 0 ? void 0 : data.user),\n            userId: data === null || data === void 0 ? void 0 : (_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id,\n            userEmail: data === null || data === void 0 ? void 0 : (_data_user1 = data.user) === null || _data_user1 === void 0 ? void 0 : _data_user1.email,\n            emailConfirmed: data === null || data === void 0 ? void 0 : (_data_user2 = data.user) === null || _data_user2 === void 0 ? void 0 : _data_user2.email_confirmed_at,\n            userMetadata: data === null || data === void 0 ? void 0 : (_data_user3 = data.user) === null || _data_user3 === void 0 ? void 0 : _data_user3.user_metadata,\n            error: error === null || error === void 0 ? void 0 : error.message,\n            errorCode: error === null || error === void 0 ? void 0 : error.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if ((data === null || data === void 0 ? void 0 : data.user) && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                options: {\n                    redirectTo: \"\".concat(\"http://localhost:3000\", \"/auth/confirmed\")\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if ((data === null || data === void 0 ? void 0 : data.user) && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !(userData === null || userData === void 0 ? void 0 : userData.user)) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: {\n                    ...user.user_metadata,\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                }\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: \"\".concat(\"http://localhost:3000\", \"/auth/confirmed\")\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(\"Failed to create user profile: \".concat(error.message));\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(\"Failed to upsert user profile: \".concat(error.message));\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(\"Failed to log plan change: \".concat(error.message));\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(\"Failed to log feature access: \".concat(error.message));\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(\"Failed to fetch user profile: \".concat(error.message));\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(\"Failed to update transaction: \".concat(error.message));\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(\"Failed to activate transaction: \".concat(error.message));\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(\"Failed to get user by email: \".concat(error.message));\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/admin.ts\n"));

/***/ })

}]);