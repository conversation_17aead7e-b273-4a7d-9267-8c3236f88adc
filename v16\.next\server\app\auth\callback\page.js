/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/callback/page";
exports.ids = ["app/auth/callback/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fcallback%2Fpage&page=%2Fauth%2Fcallback%2Fpage&appPaths=%2Fauth%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fcallback%2Fpage&page=%2Fauth%2Fcallback%2Fpage&appPaths=%2Fauth%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/callback/page.tsx */ \"(rsc)/./src/app/auth/callback/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'callback',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/callback/page\",\n        pathname: \"/auth/callback\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fcallback%2Fpage&page=%2Fauth%2Fcallback%2Fpage&appPaths=%2Fauth%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/callback/page.tsx */ \"(rsc)/./src/app/auth/callback/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q2NhbGxiYWNrJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUFrSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxjYWxsYmFja1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNzcmMlNUMlNUNmZWF0dXJlcyU1QyU1Q3NoYXJlZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxmZWF0dXJlc1xcXFxzaGFyZWRcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\OposI\\\\\\\\v16\\\\\\\\src\\\\\\\\app\\\\\\\\auth\\\\\\\\callback\\\\\\\\page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/callback/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fbfb08cc9359\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZiZmIwOGNjOTM1OVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/shared/components/ClientLayout */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\");\n\n\n\nconst metadata = {\n    title: 'OposiAI - Asistente IA para Oposiciones',\n    description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones',\n    icons: {\n        icon: [\n            {\n                url: '/favicon.ico',\n                sizes: 'any'\n            },\n            {\n                url: '/logo.png',\n                type: 'image/png'\n            }\n        ],\n        apple: [\n            {\n                url: '/icon-192.png',\n                sizes: '192x192',\n                type: 'image/png'\n            }\n        ]\n    },\n    manifest: '/manifest.json'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/callback/page.tsx */ \"(ssr)/./src/app/auth/callback/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q2NhbGxiYWNrJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUFrSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxjYWxsYmFja1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(ssr)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNzcmMlNUMlNUNmZWF0dXJlcyU1QyU1Q3NoYXJlZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxmZWF0dXJlc1xcXFxzaGFyZWRcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n// src/app/auth/callback/page.tsx\n// VERSIÓN MODIFICADA CON MANEJO MEJORADO Y FLAG PARA SETUP DE CONTRASEÑA\n/* __next_internal_client_entry_do_not_use__ default auto */ \n // Añadir useRef\n\n\nfunction AuthCallbackContent() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Procesando autenticación, por favor espera...');\n    const timeoutIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // Usar ref para el ID del timeout\n    // Función para añadir logs a la consola\n    const log = (msg, data)=>{\n        const timestamp = new Date().toISOString();\n        const logEntry = `[${timestamp}] ${msg}`;\n        console.log(logEntry, data || '');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthCallbackContent.useEffect\": ()=>{\n            log('=== [AUTH-CALLBACK-PAGE] AuthCallbackContent useEffect INICIADO ===');\n            // Log inicial de la URL para ver qué llega\n            log('🌐 [AUTH-CALLBACK-PAGE] URL Completa en el cliente:', window.location.href);\n            log('📋 [AUTH-CALLBACK-PAGE] Query Params (searchParams):', Object.fromEntries(searchParams.entries()));\n            log('🔍 [AUTH-CALLBACK-PAGE] Verificando si llegamos desde el servidor o directamente...');\n            const errorDescription = searchParams.get('error_description');\n            const errorCode = searchParams.get('error_code');\n            const errorParam = searchParams.get('error');\n            if (errorDescription || errorCode || errorParam) {\n                let friendlyMessage = decodeURIComponent(errorDescription || errorParam || 'Error desconocido');\n                if (errorCode === 'user_already_invited' || errorDescription && errorDescription.includes('User already invited')) {\n                    friendlyMessage = 'Ya se ha enviado una invitación a este correo. Por favor, revisa tu bandeja de entrada (y spam). Si no la encuentras, puedes intentar recuperar tu contraseña desde la página de login.';\n                } else if (errorCode === 'token_expired_or_invalid' || errorDescription && (errorDescription.includes('invalid token') || errorDescription.includes('expired'))) {\n                    friendlyMessage = 'El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta registrarte o iniciar sesión de nuevo.';\n                } else if (friendlyMessage.includes('Código de autenticación no encontrado')) {\n                    friendlyMessage = 'El enlace de confirmación no es válido. Por favor, verifica que hayas copiado la URL completa del email.';\n                } else if (friendlyMessage.includes('No se pudo completar la autenticación')) {\n                    friendlyMessage = 'No se pudo completar la verificación de tu email. Por favor, intenta usar el enlace del email nuevamente.';\n                }\n                log('❌ Error explícito detectado en la URL.', {\n                    error: friendlyMessage,\n                    code: errorCode,\n                    error_param: errorParam\n                });\n                setStatus('error');\n                setMessage(friendlyMessage);\n                if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current); // Limpiar timeout si hay error\n                return;\n            }\n            const supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            log('Cliente Supabase del navegador inicializado.');\n            // Verificar si hay código en la URL y dar tiempo a Supabase para procesarlo\n            const code = searchParams.get('code');\n            if (code) {\n                log('🔑 [AUTH-CALLBACK-PAGE] Código encontrado en URL:', code.substring(0, 10) + '...');\n                log('⏳ [AUTH-CALLBACK-PAGE] Esperando que Supabase procese automáticamente el código...');\n                // Dar tiempo a Supabase para procesar el código automáticamente\n                setTimeout({\n                    \"AuthCallbackContent.useEffect\": ()=>{\n                        supabase.auth.getSession().then({\n                            \"AuthCallbackContent.useEffect\": ({ data: { session } })=>{\n                                if (session) {\n                                    log('✅ [AUTH-CALLBACK-PAGE] Sesión encontrada después del procesamiento automático');\n                                // El listener onAuthStateChange debería detectar esto\n                                } else {\n                                    log('⚠️ [AUTH-CALLBACK-PAGE] No hay sesión después del procesamiento automático, verificando...');\n                                // Si no hay sesión, el listener INITIAL_SESSION lo manejará\n                                }\n                            }\n                        }[\"AuthCallbackContent.useEffect\"]);\n                    }\n                }[\"AuthCallbackContent.useEffect\"], 1000); // Dar 1 segundo para que Supabase procese\n            }\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthCallbackContent.useEffect\": async (event, session)=>{\n                    log(`EVENTO onAuthStateChange RECIBIDO: ${event}`, {\n                        hasSession: !!session,\n                        userId: session?.user?.id,\n                        userMetadata: session?.user?.user_metadata\n                    });\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current); // Limpiar el timeout si se maneja un evento\n                        timeoutIdRef.current = null;\n                    }\n                    if (event === 'PASSWORD_RECOVERY') {\n                        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');\n                        setStatus('success');\n                        setMessage('Sesión de recuperación lista. Redirigiendo para establecer contraseña...');\n                        router.push('/auth/reset-password');\n                    // ===== MODIFICACIÓN START (Lógica para requires_password_setup en SIGNED_IN) =====\n                    } else if (event === 'SIGNED_IN') {\n                        log('✅ Evento SIGNED_IN detectado.');\n                        if (session?.user) {\n                            // Verificar si el usuario necesita configurar su contraseña\n                            const userRequiresPasswordSetup = session.user.user_metadata?.requires_password_setup === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Usuario necesita establecer contraseña inicial. Redirigiendo a /auth/reset-password...');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                // /auth/reset-password manejará la sesión actual y permitirá actualizar la contraseña\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Usuario ya tiene contraseña o no requiere setup. Redirigiendo a /app...');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('⚠️ Evento SIGNED_IN sin sesión completa. Comportamiento inesperado.');\n                            setStatus('error');\n                            setMessage('Error inesperado durante el inicio de sesión. Por favor, inténtalo de nuevo.');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'USER_UPDATED' && session?.user) {\n                        // Este evento puede ser útil si la confirmación de email ocurre y luego se necesita setear contraseña.\n                        log('✅ Evento USER_UPDATED con sesión.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en USER_UPDATED) =====\n                        const userRequiresPasswordSetup = session.user.user_metadata?.requires_password_setup === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Usuario actualizado necesita establecer contraseña. Redirigiendo a /auth/reset-password...');\n                            setStatus('success');\n                            setMessage('Actualización de cuenta completada. Crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Cuenta actualizada. Redirigiendo a /app...');\n                            setStatus('success');\n                            setMessage('Cuenta actualizada. Redirigiendo...');\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'INITIAL_SESSION' && !session) {\n                        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos o timeout.');\n                    // No hacer nada, esperar al timeout si no llegan más eventos.\n                    } else if (event === 'INITIAL_SESSION' && session?.user) {\n                        // Se encontró una sesión inicial, puede ser de una pestaña anterior o un token válido procesado rápido.\n                        log('✅ Evento INITIAL_SESSION con sesión. Procediendo como SIGNED_IN.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en INITIAL_SESSION) =====\n                        const userRequiresPasswordSetup = session.user.user_metadata?.requires_password_setup === true;\n                        if (userRequiresPasswordSetup) {\n                            router.push('/auth/reset-password');\n                        } else {\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"]);\n            log('Listener onAuthStateChange configurado.');\n            // Verificación inmediata de la sesión (por si ya está autenticado)\n            const checkCurrentSession = {\n                \"AuthCallbackContent.useEffect.checkCurrentSession\": async ()=>{\n                    const { data: { session: currentSession } } = await supabase.auth.getSession();\n                    if (currentSession?.user && status === 'loading') {\n                        log('✅ Sesión ya existente encontrada inmediatamente.');\n                        const userRequiresPasswordSetup = currentSession.user.user_metadata?.requires_password_setup === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Redirigiendo a /auth/reset-password (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Redirigiendo a /app (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Autenticación exitosa! Redirigiendo...');\n                            router.push('/app');\n                        }\n                        if (timeoutIdRef.current) {\n                            clearTimeout(timeoutIdRef.current);\n                            timeoutIdRef.current = null;\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect.checkCurrentSession\"];\n            // Ejecutar verificación inmediata\n            checkCurrentSession();\n            // Timeout reducido para casos donde no se recibe el evento esperado\n            timeoutIdRef.current = setTimeout({\n                \"AuthCallbackContent.useEffect\": async ()=>{\n                    if (status === 'loading') {\n                        const { data: { session: finalCheckSession }, error: finalCheckError } = await supabase.auth.getSession();\n                        if (finalCheckSession?.user) {\n                            log('✅ Sesión encontrada en la verificación final del timeout.');\n                            const userRequiresPasswordSetup = finalCheckSession.user.user_metadata?.requires_password_setup === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Redirigiendo a /auth/reset-password desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Redirigiendo a /app desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('❌ TIMEOUT (5s): No se recibió un evento de autenticación válido y no hay sesión activa.');\n                            log('Detalles del error en la verificación final del timeout:', finalCheckError?.message);\n                            setStatus('error');\n                            setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Si el problema persiste, intenta registrarte de nuevo o contacta a soporte.');\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"], 5000); // Reducido a 5 segundos\n            return ({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    log('--- AuthCallbackContent useEffect LIMPIEZA ---');\n                    authListener?.subscription.unsubscribe();\n                    log('Suscripción a onAuthStateChange eliminada.');\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                        log('Temporizador de timeout limpiado.');\n                    }\n                }\n            })[\"AuthCallbackContent.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthCallbackContent.useEffect\"], [\n        router,\n        searchParams\n    ]);\n    // ... (resto del componente JSX sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Procesando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"\\xa1\\xc9xito!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Error de Autenticaci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/login'),\n                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                                children: \"Ir a Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\nfunction AuthCallbackPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Procesando autenticaci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthCallbackContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/callback/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/BackgroundTasksPanel.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/BackgroundTasksPanel.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BackgroundTasksPanel = ()=>{\n    const { activeTasks, completedTasks, removeTask, clearCompletedTasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__.useBackgroundTasks)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCompleted, setShowCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const totalTasks = activeTasks.length + completedTasks.length;\n    if (totalTasks === 0) {\n        return null;\n    }\n    const getTaskIcon = (task)=>{\n        switch(task.status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 16\n                }, undefined);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 16\n                }, undefined);\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 16\n                }, undefined);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTaskTypeLabel = (type)=>{\n        switch(type){\n            case 'mapa-mental':\n                return 'Mapa Mental';\n            case 'test':\n                return 'Test';\n            case 'flashcards':\n                return 'Flashcards';\n            default:\n                return 'Tarea';\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('es-ES', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between\",\n                    onClick: ()=>setIsExpanded(!isExpanded),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"Tareas (\",\n                                        activeTasks.length,\n                                        \" activas)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-96 overflow-y-auto\",\n                    children: [\n                        activeTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border-b border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-gray-700 mb-2\",\n                                    children: \"Tareas Activas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: activeTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        task.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-200 rounded-full h-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-500 h-1.5 rounded-full transition-all duration-300\",\n                                                                    style: {\n                                                                        width: `${task.progress}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: formatTime(task.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, undefined),\n                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompleted(!showCompleted),\n                                            className: \"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Completadas (\",\n                                                        completedTasks.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                showCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearCompletedTasks,\n                                            className: \"text-xs text-gray-500 hover:text-red-600 transition-colors\",\n                                            children: \"Limpiar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, undefined),\n                                showCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: completedTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        task.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-500 truncate\",\n                                                            children: task.error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: task.completedAt ? formatTime(task.completedAt) : formatTime(task.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeTask(task.id),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackgroundTasksPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/BackgroundTasksPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(ssr)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useInactivityTimer */ \"(ssr)/./src/hooks/useInactivityTimer.ts\");\n/* harmony import */ var _features_auth_components_InactivityWarning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/auth/components/InactivityWarning */ \"(ssr)/./src/features/auth/components/InactivityWarning.tsx\");\n\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start true: loading initial auth state\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInactivityWarning, setShowInactivityWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [warningTimeRemaining, setWarningTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60); // 60 segundos de advertencia\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Effect for auth state listener and initial session check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true); // Explicitly set loading true at the start of auth setup\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, currentSession)=>{\n                    setSession(currentSession);\n                    setUser(currentSession?.user ?? null);\n                    setError(null); // Clear previous errors on any auth state change\n                    // Centralize setIsLoading(false) after processing the event.\n                    if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.\n            _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session: initialSessionCheck }, error: getSessionError })=>{\n                    if (getSessionError) {\n                        setError(getSessionError.message);\n                        setIsLoading(false); // Ensure loading is false if initial getSession fails\n                    }\n                    // Para dispositivos móviles, verificar también localStorage si no hay sesión\n                    if (!initialSessionCheck && \"undefined\" !== 'undefined') {}\n                // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.\n                // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.\n                }\n            }[\"AuthProvider.useEffect\"]).catch({\n                \"AuthProvider.useEffect\": (error)=>{\n                    setError(error.message);\n                    setIsLoading(false); // Ensure loading is false if initial getSession throws\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Runs once on mount\n    // Effect for handling redirections based on auth state and pathname\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // No realizar redirecciones mientras se está cargando\n            if (isLoading) {\n                return;\n            }\n            // No aplicar redirecciones a rutas de API o recursos estáticos\n            if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {\n                return;\n            }\n            // Definir rutas públicas (mantener sincronizado con middleware)\n            // src/contexts/AuthContext.tsx (CORREGIDO)\n            const publicPaths = [\n                '/',\n                '/login',\n                '/payment',\n                '/thank-you',\n                '/auth/callback',\n                '/auth/confirmed',\n                '/auth/unauthorized',\n                '/auth/reset-password',\n                '/auth/confirm-reset',\n                '/auth/confirm-invitation' // <-- **AÑADIR ESTA LÍNEA**\n            ];\n            // Si hay sesión y estamos en /login, redirigir a la aplicación\n            // Esta es una salvaguarda del lado del cliente.\n            if (session && pathname === '/login') {\n                router.replace('/app'); // router.replace es mejor aquí para evitar entradas en el historial\n                return; // Importante retornar para no evaluar la siguiente condición\n            }\n            // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)\n            // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)\n            if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {\n                router.replace('/login');\n                return;\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        session,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const iniciarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[iniciarSesion]\": async (email, password_provided)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const { user: loggedInUser, session: currentAuthSession, error: loginError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.iniciarSesion)(email, password_provided);\n                if (loginError) {\n                    setError(loginError);\n                    setIsLoading(false); // Ensure loading is false on error\n                    return {\n                        user: null,\n                        session: null,\n                        error: loginError\n                    };\n                }\n                // Verificar que la sesión se haya establecido correctamente antes de redirigir\n                if (currentAuthSession) {\n                    // Esperar un momento adicional para asegurar que las cookies se propaguen\n                    // antes de la redirección\n                    await new Promise({\n                        \"AuthProvider.useCallback[iniciarSesion]\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"AuthProvider.useCallback[iniciarSesion]\"]);\n                    // Redirigir a la aplicación usando replace para evitar entradas en el historial\n                    router.replace('/app');\n                }\n                // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.\n                return {\n                    user: loggedInUser,\n                    session: currentAuthSession,\n                    error: null\n                };\n            } catch (e) {\n                const errorMessage = e instanceof Error && e.message ? e.message : 'Error desconocido durante el inicio de sesión.';\n                setError(errorMessage);\n                setIsLoading(false); // Ensure loading is false on exception\n                return {\n                    user: null,\n                    session: null,\n                    error: errorMessage\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[iniciarSesion]\"], [\n        router\n    ]); // Added router dependency\n    const cerrarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[cerrarSesion]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            const { error: logoutError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.cerrarSesion)();\n            if (logoutError) {\n                setError(logoutError);\n                setIsLoading(false); // Ensure loading is false on error\n            }\n        // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.\n        // The redirection useEffect will then handle redirecting to /login.\n        }\n    }[\"AuthProvider.useCallback[cerrarSesion]\"], []); // Assuming cerrarSesionService is a stable import\n    const estaAutenticado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[estaAutenticado]\": ()=>!!user && !!session && !isLoading\n    }[\"AuthProvider.useCallback[estaAutenticado]\"], [\n        user,\n        session,\n        isLoading\n    ]);\n    // Función para manejar el logout por inactividad\n    const handleInactivityLogout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleInactivityLogout]\": async ()=>{\n            // Cerrar sesión directamente sin mostrar advertencia\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleInactivityLogout]\"], [\n        cerrarSesion\n    ]);\n    // Función para extender la sesión\n    const handleExtendSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleExtendSession]\": ()=>{\n            setShowInactivityWarning(false);\n        // El hook useAutoLogout se reiniciará automáticamente con la actividad\n        }\n    }[\"AuthProvider.useCallback[handleExtendSession]\"], []);\n    // Función para cerrar sesión desde la advertencia\n    const handleLogoutFromWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLogoutFromWarning]\": async ()=>{\n            setShowInactivityWarning(false);\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleLogoutFromWarning]\"], [\n        cerrarSesion\n    ]);\n    // Hook para manejar la inactividad (solo si el usuario está autenticado)\n    const { resetTimer } = (0,_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__.useAutoLogout)(5, handleInactivityLogout, estaAutenticado() // Solo activo si está autenticado\n    );\n    const value = {\n        user,\n        session,\n        isLoading,\n        error,\n        iniciarSesion,\n        cerrarSesion,\n        estaAutenticado\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: [\n            children,\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/BackgroundTasksContext.tsx":
/*!*************************************************!*\
  !*** ./src/contexts/BackgroundTasksContext.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundTasksProvider: () => (/* binding */ BackgroundTasksProvider),\n/* harmony export */   useBackgroundTasks: () => (/* binding */ useBackgroundTasks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ BackgroundTasksProvider,useBackgroundTasks auto */ \n\n\nconst BackgroundTasksContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst BackgroundTasksProvider = ({ children })=>{\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[addTask]\": (taskData)=>{\n            const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTask = {\n                ...taskData,\n                id,\n                status: 'pending',\n                createdAt: new Date()\n            };\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[addTask]\": (prev)=>[\n                        ...prev,\n                        newTask\n                    ]\n            }[\"BackgroundTasksProvider.useCallback[addTask]\"]);\n            // Mostrar notificación de inicio\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Iniciando: ${newTask.title}`, {\n                id: `task_start_${id}`,\n                duration: 2000\n            });\n            return id;\n        }\n    }[\"BackgroundTasksProvider.useCallback[addTask]\"], []);\n    const updateTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[updateTask]\": (id, updates)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[updateTask]\": (prev)=>prev.map({\n                        \"BackgroundTasksProvider.useCallback[updateTask]\": (task)=>{\n                            if (task.id === id) {\n                                const updatedTask = {\n                                    ...task,\n                                    ...updates\n                                };\n                                // Manejar notificaciones según el estado\n                                if (updates.status === 'processing' && task.status === 'pending') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Procesando: ${task.title}`, {\n                                        id: `task_processing_${id}`\n                                    });\n                                } else if (updates.status === 'completed' && task.status !== 'completed') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(`Completado: ${task.title}`, {\n                                        id: `task_completed_${id}`,\n                                        duration: 4000\n                                    });\n                                    updatedTask.completedAt = new Date();\n                                } else if (updates.status === 'error' && task.status !== 'error') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(`Error: ${task.title}`, {\n                                        id: `task_error_${id}`,\n                                        duration: 5000\n                                    });\n                                }\n                                return updatedTask;\n                            }\n                            return task;\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[updateTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[updateTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[updateTask]\"], []);\n    const removeTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[removeTask]\": (id)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[removeTask]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[removeTask]\": (task)=>task.id !== id\n                    }[\"BackgroundTasksProvider.useCallback[removeTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[removeTask]\"]);\n            // Limpiar notificaciones relacionadas\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_completed_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_error_${id}`);\n        }\n    }[\"BackgroundTasksProvider.useCallback[removeTask]\"], []);\n    const getTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTask]\": (id)=>{\n            return tasks.find({\n                \"BackgroundTasksProvider.useCallback[getTask]\": (task)=>task.id === id\n            }[\"BackgroundTasksProvider.useCallback[getTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTask]\"], [\n        tasks\n    ]);\n    const getTasksByType = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTasksByType]\": (type)=>{\n            return tasks.filter({\n                \"BackgroundTasksProvider.useCallback[getTasksByType]\": (task)=>task.type === type\n            }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"], [\n        tasks\n    ]);\n    const clearCompletedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": ()=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (task)=>task.status !== 'completed' && task.status !== 'error'\n                    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"])\n            }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"], []);\n    const activeTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[activeTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[activeTasks]\": (task)=>task.status === 'pending' || task.status === 'processing'\n            }[\"BackgroundTasksProvider.useMemo[activeTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[activeTasks]\"], [\n        tasks\n    ]);\n    const completedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[completedTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[completedTasks]\": (task)=>task.status === 'completed' || task.status === 'error'\n            }[\"BackgroundTasksProvider.useMemo[completedTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[completedTasks]\"], [\n        tasks\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[value]\": ()=>({\n                tasks,\n                addTask,\n                updateTask,\n                removeTask,\n                getTask,\n                getTasksByType,\n                clearCompletedTasks,\n                activeTasks,\n                completedTasks\n            })\n    }[\"BackgroundTasksProvider.useMemo[value]\"], [\n        tasks,\n        addTask,\n        updateTask,\n        removeTask,\n        getTask,\n        getTasksByType,\n        clearCompletedTasks,\n        activeTasks,\n        completedTasks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackgroundTasksContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\contexts\\\\BackgroundTasksContext.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\nconst useBackgroundTasks = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BackgroundTasksContext);\n    if (context === undefined) {\n        throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/BackgroundTasksContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/AuthManager.tsx":
/*!******************************************************!*\
  !*** ./src/features/auth/components/AuthManager.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Componente para manejar errores comunes de autenticación\n * y sincronización de tiempo en Supabase\n */ function AuthManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthManager.useEffect\": ()=>{\n            // Verificar si hay problemas de sincronización de tiempo\n            const checkTimeSync = {\n                \"AuthManager.useEffect.checkTimeSync\": async ()=>{\n                    try {\n                        // Usar variables de entorno para la configuración de Supabase\n                        const supabaseUrl = \"https://fxnhpxjijinfuxxxplzj.supabase.co\";\n                        const supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\";\n                        if (!supabaseUrl || !supabaseKey) {\n                            console.warn('Variables de entorno de Supabase no configuradas');\n                            return;\n                        }\n                        // Obtener la hora del servidor de Supabase\n                        const response = await fetch(`${supabaseUrl}/rest/v1/`, {\n                            method: 'GET',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'apikey': supabaseKey\n                            }\n                        });\n                        // Obtener la fecha del servidor desde las cabeceras\n                        const serverDate = new Date(response.headers.get('date') || '');\n                        const clientDate = new Date();\n                        // Calcular la diferencia en segundos\n                        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);\n                        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia\n                        if (timeDiff > 60) {\n                            console.warn(`Posible problema de sincronización de tiempo detectado. ` + `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` + `Esto puede causar problemas de autenticación.`);\n                        }\n                    } catch (error) {\n                        console.error('Error al verificar sincronización de tiempo:', error);\n                    }\n                }\n            }[\"AuthManager.useEffect.checkTimeSync\"];\n            // Ejecutar la verificación\n            checkTimeSync();\n            // Configurar un listener para eventos de autenticación\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"AuthManager.useEffect\": (event, session)=>{\n                    if (event === 'SIGNED_OUT') {\n                    // Supabase ya maneja la limpieza de tokens internamente\n                    } else if (event === 'SIGNED_IN') {}\n                }\n            }[\"AuthManager.useEffect\"]);\n            return ({\n                \"AuthManager.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthManager.useEffect\"];\n        }\n    }[\"AuthManager.useEffect\"], []);\n    // Este componente no renderiza nada visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/AuthManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/InactivityWarning.tsx":
/*!************************************************************!*\
  !*** ./src/features/auth/components/InactivityWarning.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiClock,FiRefreshCw!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst InactivityWarning = ({ isVisible, timeRemaining, onExtendSession, onLogout })=>{\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(timeRemaining);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            setCountdown(timeRemaining);\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        timeRemaining\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            if (!isVisible || countdown <= 0) return;\n            const interval = setInterval({\n                \"InactivityWarning.useEffect.interval\": ()=>{\n                    setCountdown({\n                        \"InactivityWarning.useEffect.interval\": (prev)=>{\n                            if (prev <= 1) {\n                                onLogout();\n                                return 0;\n                            }\n                            return prev - 1;\n                        }\n                    }[\"InactivityWarning.useEffect.interval\"]);\n                }\n            }[\"InactivityWarning.useEffect.interval\"], 1000);\n            return ({\n                \"InactivityWarning.useEffect\": ()=>clearInterval(interval)\n            })[\"InactivityWarning.useEffect\"];\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        isVisible,\n        countdown,\n        onLogout\n    ]);\n    if (!isVisible) return null;\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-100 rounded-full p-3 mr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiAlertTriangle, {\n                                className: \"text-yellow-600 text-xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Sesi\\xf3n por expirar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Tu sesi\\xf3n expirar\\xe1 por inactividad\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                                    className: \"text-gray-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-mono font-bold text-gray-900\",\n                                    children: formatTime(countdown)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 mt-2\",\n                            children: \"Tiempo restante antes del cierre autom\\xe1tico\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onExtendSession,\n                            className: \"flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Continuar sesi\\xf3n\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onLogout,\n                            className: \"flex-1 bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n                            children: \"Cerrar sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Por seguridad, tu sesi\\xf3n se cerrar\\xe1 autom\\xe1ticamente tras 10 minutos de inactividad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InactivityWarning);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/InactivityWarning.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../auth/components/AuthManager */ \"(ssr)/./src/features/auth/components/AuthManager.tsx\");\n/* harmony import */ var _components_ui_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/BackgroundTasksPanel */ \"(ssr)/./src/components/ui/BackgroundTasksPanel.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n // Importar Toaster\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__.BackgroundTasksProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.Toaster, {\n                    position: \"top-right\" // Posición de los toasts\n                    ,\n                    toastOptions: {\n                        // Opciones por defecto para los toasts\n                        duration: 5000,\n                        style: {\n                            background: '#363636',\n                            color: '#fff'\n                        },\n                        success: {\n                            duration: 3000,\n                            style: {\n                                background: '#10b981',\n                                color: '#fff'\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            style: {\n                                background: '#ef4444',\n                                color: '#fff'\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useInactivityTimer.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useInactivityTimer.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout),\n/* harmony export */   useInactivityTimer: () => (/* binding */ useInactivityTimer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n\n\n// Eventos que consideramos como actividad del usuario (definido fuera del hook para estabilidad)\nconst ACTIVITY_EVENTS = [\n    'mousedown',\n    'mousemove',\n    'keypress',\n    'scroll',\n    'touchstart',\n    'click'\n];\n/**\n * Hook para manejar la desconexión automática por inactividad\n */ const useInactivityTimer = ({ timeout, onTimeout, enabled = true })=>{\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const { tasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__.useBackgroundTasks)();\n    // Verificar si hay tareas de IA activas que deberían pausar el timer\n    const hasActiveAITasks = tasks.some((task)=>task.status === 'pending' || task.status === 'processing');\n    // Función para resetear el timer\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n            if (!enabled) return;\n            // Limpiar el timer anterior si existe\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            // Actualizar la última actividad\n            lastActivityRef.current = Date.now();\n            // Siempre crear un nuevo timer, independientemente de las tareas de IA\n            // La actividad del usuario siempre reinicia el timer\n            timeoutRef.current = setTimeout({\n                \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n                    // Solo ejecutar logout si no hay tareas de IA activas en el momento del timeout\n                    const currentTasks = tasks.filter({\n                        \"useInactivityTimer.useCallback[resetTimer].currentTasks\": (task)=>task.status === 'pending' || task.status === 'processing'\n                    }[\"useInactivityTimer.useCallback[resetTimer].currentTasks\"]);\n                    if (currentTasks.length === 0) {\n                        onTimeout();\n                    } else {\n                        console.log('🔄 Logout diferido: hay tareas de IA en progreso, reintentando en 1 minuto');\n                        // Reintentar en 1 minuto si hay tareas activas\n                        setTimeout({\n                            \"useInactivityTimer.useCallback[resetTimer]\": ()=>resetTimer()\n                        }[\"useInactivityTimer.useCallback[resetTimer]\"], 60000);\n                    }\n                }\n            }[\"useInactivityTimer.useCallback[resetTimer]\"], timeout);\n        }\n    }[\"useInactivityTimer.useCallback[resetTimer]\"], [\n        timeout,\n        onTimeout,\n        enabled,\n        tasks\n    ]);\n    // Función para limpiar el timer\n    const clearTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[clearTimer]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n        }\n    }[\"useInactivityTimer.useCallback[clearTimer]\"], []);\n    // Función para obtener el tiempo restante\n    const getTimeRemaining = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[getTimeRemaining]\": ()=>{\n            if (!enabled || !timeoutRef.current) return 0;\n            const elapsed = Date.now() - lastActivityRef.current;\n            const remaining = Math.max(0, timeout - elapsed);\n            return remaining;\n        }\n    }[\"useInactivityTimer.useCallback[getTimeRemaining]\"], [\n        timeout,\n        enabled\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useInactivityTimer.useEffect\": ()=>{\n            if (!enabled) {\n                clearTimer();\n                return;\n            }\n            // Función que maneja los eventos de actividad\n            const handleActivity = {\n                \"useInactivityTimer.useEffect.handleActivity\": ()=>{\n                    resetTimer();\n                }\n            }[\"useInactivityTimer.useEffect.handleActivity\"];\n            // Agregar event listeners\n            ACTIVITY_EVENTS.forEach({\n                \"useInactivityTimer.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useInactivityTimer.useEffect\"]);\n            // Iniciar el timer\n            resetTimer();\n            // Cleanup\n            return ({\n                \"useInactivityTimer.useEffect\": ()=>{\n                    ACTIVITY_EVENTS.forEach({\n                        \"useInactivityTimer.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useInactivityTimer.useEffect\"]);\n                    clearTimer();\n                }\n            })[\"useInactivityTimer.useEffect\"];\n        }\n    }[\"useInactivityTimer.useEffect\"], [\n        enabled,\n        resetTimer,\n        clearTimer\n    ]);\n    return {\n        resetTimer,\n        clearTimer,\n        getTimeRemaining\n    };\n};\n/**\n * Hook simplificado para desconexión automática\n */ const useAutoLogout = (timeoutMinutes = 5, onLogout, enabled = true)=>{\n    const timeoutMs = timeoutMinutes * 60 * 1000; // Convertir minutos a milisegundos\n    return useInactivityTimer({\n        timeout: timeoutMs,\n        onTimeout: onLogout,\n        enabled\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useInactivityTimer.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\r\n * Inicia sesión con email y contraseña\r\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\r\n * Cierra la sesión del usuario actual\r\n */ async function cerrarSesion() {\n    try {\n        console.log('🔓 Iniciando proceso de logout...');\n        // Cerrar sesión con scope 'global' para limpiar tanto local como servidor\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut({\n            scope: 'global'\n        });\n        if (error) {\n            console.error('❌ Error en signOut:', error.message);\n            return {\n                error: error.message\n            };\n        }\n        console.log('✅ SignOut exitoso');\n        // Limpiar cualquier dato de sesión residual del localStorage/sessionStorage\n        if (false) {}\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('❌ Error inesperado en logout:', error);\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\r\n * Obtiene la sesión actual del usuario\r\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\r\n * Obtiene el usuario actual\r\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\r\n * Verifica si el usuario está autenticado\r\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: true,\n            autoRefreshToken: true,\n            detectSessionInUrl: true // ESENCIAL: Detectar y procesar tokens de URL\n        }\n    });\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFFcEQsc0RBQXNEO0FBQy9DLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUksTUFBTTtZQUNKQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsb0JBQW9CLEtBQVEsOENBQThDO1FBQzVFO0lBQ0Y7QUFFSjtBQUVBLCtDQUErQztBQUN4QyxNQUFNQyxXQUFXVCxlQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIG5hdmVnYWRvciAoY29tcG9uZW50ZXMgZGVsIGNsaWVudGUpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZISxcbiAgICB7XG4gICAgICBhdXRoOiB7XG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLCAgICAgICAvLyBQZXJzaXN0aXIgc2VzacOzbiBlbiBlbCBuYXZlZ2Fkb3JcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSwgICAgIC8vIFJlZnJlc2NhciB0b2tlbiBhdXRvbcOhdGljYW1lbnRlXG4gICAgICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZSAgICAvLyBFU0VOQ0lBTDogRGV0ZWN0YXIgeSBwcm9jZXNhciB0b2tlbnMgZGUgVVJMXG4gICAgICB9XG4gICAgfVxuICApO1xufVxuXG4vLyBNYW50ZW5lciBjb21wYXRpYmlsaWRhZCBjb24gY8OzZGlnbyBleGlzdGVudGVcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJhdXRoIiwicGVyc2lzdFNlc3Npb24iLCJhdXRvUmVmcmVzaFRva2VuIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   supabase: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _types_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/database */ \"(ssr)/./src/types/database.ts\");\n// Solo re-exportar el cliente del navegador para mantener compatibilidad\n\n// NOTA: Para usar el cliente del servidor, importar directamente desde './server'\n// import { createServerSupabaseClient } from '@/lib/supabase/server';\n// Re-exportar todos los tipos desde el archivo centralizado de tipos de base de datos\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL3N1cGFiYXNlQ2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSx5RUFBeUU7QUFDdkI7QUFFbEQsa0ZBQWtGO0FBQ2xGLHNFQUFzRTtBQUV0RSxzRkFBc0Y7QUFDckQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcc3JjXFxsaWJcXHN1cGFiYXNlXFxzdXBhYmFzZUNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTb2xvIHJlLWV4cG9ydGFyIGVsIGNsaWVudGUgZGVsIG5hdmVnYWRvciBwYXJhIG1hbnRlbmVyIGNvbXBhdGliaWxpZGFkXHJcbmV4cG9ydCB7IGNyZWF0ZUNsaWVudCwgc3VwYWJhc2UgfSBmcm9tICcuL2NsaWVudCc7XHJcblxyXG4vLyBOT1RBOiBQYXJhIHVzYXIgZWwgY2xpZW50ZSBkZWwgc2Vydmlkb3IsIGltcG9ydGFyIGRpcmVjdGFtZW50ZSBkZXNkZSAnLi9zZXJ2ZXInXHJcbi8vIGltcG9ydCB7IGNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50IH0gZnJvbSAnQC9saWIvc3VwYWJhc2Uvc2VydmVyJztcclxuXHJcbi8vIFJlLWV4cG9ydGFyIHRvZG9zIGxvcyB0aXBvcyBkZXNkZSBlbCBhcmNoaXZvIGNlbnRyYWxpemFkbyBkZSB0aXBvcyBkZSBiYXNlIGRlIGRhdG9zXHJcbmV4cG9ydCAqIGZyb20gJ0AvdHlwZXMvZGF0YWJhc2UnO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/database.ts":
/*!*******************************!*\
  !*** ./src/types/database.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/**\n * Tipos relacionados con la base de datos Supabase\n *\n * Este archivo contiene todas las interfaces y tipos que representan\n * las entidades de la base de datos y sus relaciones.\n */ // ============================================================================\n// TIPOS BÁSICOS Y ENUMS\n// ============================================================================\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/database.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/react-icons","vendor-chunks/react-hot-toast","vendor-chunks/@heroicons","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fcallback%2Fpage&page=%2Fauth%2Fcallback%2Fpage&appPaths=%2Fauth%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();