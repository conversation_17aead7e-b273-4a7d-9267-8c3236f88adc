// src/app/api/stripe/create-checkout-session/route.ts
import { NextResponse } from 'next/server';
import { stripe, getPlanById, isValidPlan, APP_URLS } from '@/lib/stripe/config';

export async function POST(request: Request) {
  try {
    console.log('Stripe API called');
    
    const body = await request.json();
    console.log('Request body:', body);
    
    const { planId, email, customerName, userId, registrationData } = body;

    // Validaciones básicas
    if (!planId || !email) {
      console.log('Missing planId or email');
      return NextResponse.json({
        error: 'Plan ID y email son requeridos'
      }, { status: 400 });
    }

    console.log('Validating plan:', planId);
    
    if (!isValidPlan(planId)) {
      console.log('Invalid plan ID:', planId);
      return NextResponse.json({
        error: 'Plan ID no válido'
      }, { status: 400 });
    }

    const plan = getPlanById(planId);
    console.log('Plan found:', plan);
    
    if (!plan) {
      console.log('Plan not found');
      return NextResponse.json({
        error: 'Plan no encontrado'
      }, { status: 404 });
    }

    // El plan gratuito no requiere pago
    if (planId === 'free') {
      console.log('Free plan does not require payment');
      return NextResponse.json({
        error: 'El plan gratuito no requiere pago'
      }, { status: 400 });
    }

    console.log('Creating checkout session for:', { planId, email, customerName });

    // Usar el precio fijo configurado
    const priceId = plan.stripePriceId;
    
    if (!priceId) {
      console.log('No price ID configured for plan:', planId);
      return NextResponse.json({
        error: 'Precio no configurado para este plan'
      }, { status: 500 });
    }

    console.log('Using price ID:', priceId);

    // Determinar el modo de pago basado en el plan
    // Ahora 'usuario' y 'pro' son recurrentes (suscripciones mensuales)
    const isRecurring = planId === 'pro' || planId === 'usuario';
    const mode = isRecurring ? 'subscription' : 'payment';

    console.log('Payment mode:', mode, 'for plan:', planId);

    // Crear sesión de checkout
    const sessionConfig: any = {
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: mode,
      customer_email: email,
      client_reference_id: planId,
      metadata: {
        planId: planId,
        customerEmail: email,
        customerName: customerName || '',
        userId: userId || '', // ID del usuario pre-registrado (vacío para nuevo flujo)
        registrationData: registrationData ? JSON.stringify(registrationData) : '', // Datos de registro para crear cuenta después del pago
        createdAt: new Date().toISOString(),
        source: 'oposiai_website',
        autoActivate: 'true'
      },
      success_url: `${APP_URLS.success}?session_id={CHECKOUT_SESSION_ID}&plan=${planId}`,
      cancel_url: `${APP_URLS.cancel}?plan=${planId}`,
      automatic_tax: {
        enabled: true,
      },
      billing_address_collection: 'required',
      allow_promotion_codes: true,
    };

    // Agregar configuración específica para suscripciones (AHORA APLICA A 'usuario' Y 'pro')
    if (isRecurring) {
      sessionConfig.subscription_data = {
        metadata: {
          planId: planId,
          customerEmail: email,
          customerName: customerName || '',
          userId: userId || '', // ID del usuario pre-registrado (vacío para nuevo flujo)
          registrationData: registrationData ? JSON.stringify(registrationData) : '', // Datos de registro para crear cuenta después del pago
          source: 'oposiai_website',
          autoActivate: 'true'
        },
        // trial_period_days: 7, // Opcional: si quieres ofrecer un periodo de prueba
      };
    }

    if (!stripe) {
      console.error('Stripe not initialized');
      return NextResponse.json({
        error: 'Error de configuración de Stripe'
      }, { status: 500 });
    }

    const session = await stripe.checkout.sessions.create(sessionConfig);

    console.log('Checkout session created:', session.id);

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    
    let errorMessage = 'Error al crear la sesión de pago';
    if (error instanceof Error) {
      errorMessage = error.message;
      console.error('Error message:', errorMessage);
    }

    return NextResponse.json({
      error: errorMessage,
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
