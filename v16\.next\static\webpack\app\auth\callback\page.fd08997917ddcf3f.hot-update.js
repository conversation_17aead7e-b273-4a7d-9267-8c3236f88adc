"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n// ===== Archivo: src\\app\\auth\\callback\\page.tsx (CORREGIDO Y SIMPLIFICADO) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AuthCallbackContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Procesando autenticación, por favor espera...');\n    const [logMessages, setLogMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const timeoutIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const log = (msg, data)=>{\n        const timestamp = new Date().toISOString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(msg);\n        console.log(logEntry, data || '');\n        setLogMessages((prev)=>[\n                ...prev,\n                logEntry\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthCallbackContent.useEffect\": ()=>{\n            log('--- AuthCallbackContent useEffect INICIADO ---');\n            log('URL Completa en el cliente:', window.location.href);\n            const errorDescription = searchParams.get('error_description');\n            if (errorDescription) {\n                log('❌ Error explícito en la URL.', {\n                    error: errorDescription\n                });\n                setStatus('error');\n                setMessage(decodeURIComponent(errorDescription));\n                return;\n            }\n            const supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            log('Cliente Supabase del navegador inicializado.');\n            // Verificar inmediatamente si ya hay una sesión activa\n            const checkInitialSession = {\n                \"AuthCallbackContent.useEffect.checkInitialSession\": async ()=>{\n                    try {\n                        var _session_user;\n                        const { data: { session }, error } = await supabase.auth.getSession();\n                        log('🔍 Verificación inicial de sesión:', {\n                            hasSession: !!session,\n                            userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                            error: error === null || error === void 0 ? void 0 : error.message\n                        });\n                        if ((session === null || session === void 0 ? void 0 : session.user) && status === 'loading') {\n                            log('✅ Sesión activa encontrada inmediatamente. Redirigiendo...');\n                            setStatus('success');\n                            setMessage('¡Sesión encontrada! Redirigiendo...');\n                            router.push('/app');\n                            return true;\n                        }\n                        return false;\n                    } catch (error) {\n                        log('❌ Error verificando sesión inicial:', error);\n                        return false;\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect.checkInitialSession\"];\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthCallbackContent.useEffect\": async (event, session)=>{\n                    var _session_user, _session_user1;\n                    log(\"EVENTO onAuthStateChange RECIBIDO: \".concat(event), {\n                        hasSession: !!session,\n                        userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                        userEmail: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email\n                    });\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                        timeoutIdRef.current = null;\n                    }\n                    if (event === 'SIGNED_IN' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        log('✅ Evento SIGNED_IN detectado. Redirigiendo a /app...');\n                        setStatus('success');\n                        setMessage('¡Autenticación exitosa! Redirigiendo...');\n                        router.push('/app');\n                    } else if (event === 'PASSWORD_RECOVERY') {\n                        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');\n                        setStatus('success');\n                        setMessage('Sesión de recuperación lista. Redirigiendo...');\n                        router.push('/auth/reset-password');\n                    } else if (event === 'USER_UPDATED' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        log('✅ Evento USER_UPDATED con sesión. Redirigiendo a /app...');\n                        setStatus('success');\n                        setMessage('Cuenta actualizada. Redirigiendo...');\n                        router.push('/app');\n                    } else if (event === 'INITIAL_SESSION' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        log('✅ Evento INITIAL_SESSION con sesión válida. Redirigiendo a /app...');\n                        setStatus('success');\n                        setMessage('¡Sesión encontrada! Redirigiendo...');\n                        router.push('/app');\n                    } else if (event === 'INITIAL_SESSION' && !session) {\n                        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos...');\n                        // Verificar inmediatamente si hay una sesión\n                        setTimeout({\n                            \"AuthCallbackContent.useEffect\": async ()=>{\n                                const { data: { session: currentSession } } = await supabase.auth.getSession();\n                                if ((currentSession === null || currentSession === void 0 ? void 0 : currentSession.user) && status === 'loading') {\n                                    log('✅ Sesión encontrada en verificación posterior. Redirigiendo...');\n                                    setStatus('success');\n                                    setMessage('¡Sesión encontrada! Redirigiendo...');\n                                    router.push('/app');\n                                }\n                            }\n                        }[\"AuthCallbackContent.useEffect\"], 1000);\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"]);\n            log('Listener onAuthStateChange configurado. Esperando evento...');\n            // Iniciar un timeout de seguridad más largo (10-15s) para manejar casos raros\n            timeoutIdRef.current = setTimeout({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    if (status === 'loading') {\n                        log('❌ TIMEOUT (10s): No se recibió un evento de autenticación válido.');\n                        setStatus('error');\n                        setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta de nuevo.');\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"], 10000); // 10 segundos\n            return ({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    log('--- AuthCallbackContent useEffect LIMPIEZA ---');\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                    }\n                }\n            })[\"AuthCallbackContent.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthCallbackContent.useEffect\"], [\n        router\n    ]);\n    // ... (resto del componente JSX sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Procesando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"\\xa1\\xc9xito!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Error de Autenticaci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/login'),\n                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                                children: \"Ir a Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 pt-4 border-t border-gray-200 text-left bg-gray-50 p-3 rounded max-h-48 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-700 mb-2\",\n                            children: \"Registro de Eventos:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 space-y-1\",\n                            children: logMessages.map((logMsg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: logMsg\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 53\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackContent, \"fOlhbpKiY/OUV4HDYod1OUHDlWc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AuthCallbackContent;\nfunction AuthCallbackPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Procesando autenticaci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthCallbackContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AuthCallbackPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AuthCallbackContent\");\n$RefreshReg$(_c1, \"AuthCallbackPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback/page.tsx\n"));

/***/ })

});