// src/app/auth/callback/route.ts
// Endpoint para manejar callbacks de autenticación de Supabase

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const next = searchParams.get('next') ?? '/app';

  console.log('🔄 [AUTH-CALLBACK] Procesando callback de autenticación', {
    hasCode: !!code,
    next,
    origin
  });

  if (code) {
    const cookieStore = await cookies();
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch (error) {
              console.warn('[AUTH-CALLBACK] Error setting cookies:', error);
            }
          },
        },
      }
    );

    try {
      // Intercambiar el código por una sesión
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error('❌ [AUTH-CALLBACK] Error intercambiando código:', error.message);
        return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent(error.message)}`);
      }

      if (data.session && data.user) {
        console.log('✅ [AUTH-CALLBACK] Sesión creada exitosamente para usuario:', data.user.id);
        
        // Verificar si el usuario necesita configurar contraseña
        const userRequiresPasswordSetup = data.user.user_metadata?.requires_password_setup === true;
        
        if (userRequiresPasswordSetup) {
          console.log('👤 [AUTH-CALLBACK] Usuario necesita configurar contraseña, redirigiendo a reset-password');
          return NextResponse.redirect(`${origin}/auth/reset-password`);
        } else {
          console.log('🚀 [AUTH-CALLBACK] Usuario autenticado correctamente, redirigiendo a app');
          return NextResponse.redirect(`${origin}${next}`);
        }
      } else {
        console.error('❌ [AUTH-CALLBACK] No se pudo crear la sesión');
        return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent('No se pudo completar la autenticación')}`);
      }
    } catch (err) {
      console.error('❌ [AUTH-CALLBACK] Error procesando callback:', err);
      return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent('Error interno del servidor')}`);
    }
  } else {
    console.log('⚠️ [AUTH-CALLBACK] No se encontró código en la URL');
    return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent('Código de autenticación no encontrado')}`);
  }
}
