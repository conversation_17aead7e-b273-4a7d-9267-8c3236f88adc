// src/app/auth/callback/route.ts
// Endpoint para manejar callbacks de autenticación de Supabase

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const next = searchParams.get('next') ?? '/app';
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  console.log('🔄 [AUTH-CALLBACK-ROUTE] === INICIO DEL CALLBACK ===');
  console.log('🔄 [AUTH-CALLBACK-ROUTE] URL completa:', request.url);
  console.log('🔄 [AUTH-CALLBACK-ROUTE] Parámetros recibidos:', {
    hasCode: !!code,
    codeLength: code?.length,
    codePreview: code?.substring(0, 10) + '...',
    next,
    origin,
    error,
    errorDescription,
    allParams: Object.fromEntries(searchParams.entries())
  });

  if (code) {
    console.log('✅ [AUTH-CALLBACK-ROUTE] Código encontrado, iniciando intercambio...');

    const cookieStore = await cookies();
    console.log('🍪 [AUTH-CALLBACK-ROUTE] Cookie store obtenido');

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            const allCookies = cookieStore.getAll();
            console.log('🍪 [AUTH-CALLBACK-ROUTE] getAll cookies:', allCookies.length, 'cookies');
            return allCookies;
          },
          setAll(cookiesToSet) {
            console.log('🍪 [AUTH-CALLBACK-ROUTE] setAll cookies:', cookiesToSet.length, 'cookies to set');
            try {
              cookiesToSet.forEach(({ name, value, options }) => {
                console.log('🍪 [AUTH-CALLBACK-ROUTE] Setting cookie:', name);
                cookieStore.set(name, value, options);
              });
            } catch (error) {
              console.error('❌ [AUTH-CALLBACK-ROUTE] Error setting cookies:', error);
            }
          },
        },
      }
    );

    console.log('🔧 [AUTH-CALLBACK-ROUTE] Cliente Supabase creado');

    try {
      console.log('🔄 [AUTH-CALLBACK-ROUTE] Iniciando exchangeCodeForSession...');

      // Intercambiar el código por una sesión
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      console.log('📊 [AUTH-CALLBACK-ROUTE] Resultado de exchangeCodeForSession:', {
        hasData: !!data,
        hasSession: !!data?.session,
        hasUser: !!data?.user,
        userId: data?.user?.id,
        userEmail: data?.user?.email,
        hasError: !!error,
        errorMessage: error?.message,
        errorCode: error?.status
      });

      if (error) {
        console.error('❌ [AUTH-CALLBACK-ROUTE] Error intercambiando código:', {
          message: error.message,
          status: error.status,
          name: error.name
        });
        return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent(error.message)}`);
      }

      if (data.session && data.user) {
        console.log('✅ [AUTH-CALLBACK-ROUTE] Sesión creada exitosamente:', {
          userId: data.user.id,
          userEmail: data.user.email,
          sessionId: data.session.access_token?.substring(0, 10) + '...',
          userMetadata: data.user.user_metadata
        });

        // Verificar si el usuario necesita configurar contraseña
        const userRequiresPasswordSetup = data.user.user_metadata?.requires_password_setup === true;

        if (userRequiresPasswordSetup) {
          console.log('👤 [AUTH-CALLBACK-ROUTE] Usuario necesita configurar contraseña, redirigiendo a reset-password');
          return NextResponse.redirect(`${origin}/auth/reset-password`);
        } else {
          console.log('🚀 [AUTH-CALLBACK-ROUTE] Usuario autenticado correctamente, redirigiendo a:', `${origin}${next}`);
          return NextResponse.redirect(`${origin}${next}`);
        }
      } else {
        console.error('❌ [AUTH-CALLBACK-ROUTE] No se pudo crear la sesión - data incompleto:', {
          hasData: !!data,
          hasSession: !!data?.session,
          hasUser: !!data?.user
        });
        return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent('No se pudo completar la autenticación')}`);
      }
    } catch (err) {
      console.error('❌ [AUTH-CALLBACK-ROUTE] Error procesando callback:', {
        error: err,
        message: err instanceof Error ? err.message : 'Error desconocido',
        stack: err instanceof Error ? err.stack : undefined
      });
      return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent('Error interno del servidor')}`);
    }
  } else {
    console.log('⚠️ [AUTH-CALLBACK-ROUTE] No se encontró código en la URL');
    console.log('🔍 [AUTH-CALLBACK-ROUTE] Verificando si hay errores en los parámetros...');

    if (error || errorDescription) {
      console.log('❌ [AUTH-CALLBACK-ROUTE] Error encontrado en parámetros:', { error, errorDescription });
      return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent(errorDescription || error || 'Error desconocido')}`);
    }

    return NextResponse.redirect(`${origin}/auth/confirmed?error=${encodeURIComponent('Código de autenticación no encontrado')}`);
  }

  console.log('🏁 [AUTH-CALLBACK-ROUTE] === FIN DEL CALLBACK ===');
}
