/**
 * Tipos relacionados con componentes de interfaz de usuario
 *
 * Este archivo contiene interfaces y tipos para props de componentes,
 * estados de UI, y elementos de interfaz reutilizables.
 */

import { ReactNode } from 'react';

// ============================================================================
// TIPOS DE NAVEGACIÓN Y MENÚS
// ============================================================================

export type TabType = 'dashboard' | 'preguntas' | 'mapas' | 'flashcards' | 'misFlashcards' | 'tests' | 'misTests' | 'resumenes' | 'temario' | 'planEstudios' | 'gestionar';

export interface MenuItem {
  id: TabType | string;
  label: string;
  icon: ReactNode;
  color: string;
  children?: MenuItem[];
  isGroup?: boolean;
}

export interface SidebarMenuProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  children?: ReactNode;
}

// ============================================================================
// TIPOS DE MODALES
// ============================================================================

export interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface ForgotPasswordModalProps extends BaseModalProps {}

export interface InactivityWarningProps {
  isVisible: boolean;
  timeRemaining: number; // en segundos
  onExtendSession: () => void;
  onLogout: () => void;
}

// ============================================================================
// TIPOS DE COMPONENTES DE PLAN Y ACCESO
// ============================================================================

export interface UnauthorizedAccessProps {
  title?: string;
  message?: string;
  reason?: string;
  userPlan?: string;
  requiredPlan?: string[];
  feature?: string;
  showUpgradeButton?: boolean;
  showBackButton?: boolean;
  customActions?: ReactNode;
  variant?: 'card' | 'inline' | 'modal';
}

export interface UpgradePlanMessageProps {
  feature: string;
  featureDescription?: string;
  benefits?: string[];
  className?: string;
}

export interface PlanCardProps {
  id: string;
  name: string;
  price: number;
  features: readonly string[];
  isPopular?: boolean;
}

// ============================================================================
// TIPOS DE COMPONENTES DE ESTADO Y PROGRESO
// ============================================================================

export interface TokenProgressBarProps {
  used: number | null | undefined;
  limit: number | null | undefined;
  percentage: number | null | undefined;
  remaining: number | null | undefined;
}

export interface DocumentLimitStatusProps {
  className?: string;
}

export interface LimitStatus {
  current: number;
  limit: number;
  remaining: number;
  isAtLimit: boolean;
  plan: string;
}

// ============================================================================
// TIPOS DE ACCIONES Y EDICIÓN
// ============================================================================

export interface TemaActionsProps {
  tema: any; // TODO: Importar tipo Tema desde database
  onEdit: (tema: any) => void;
  onDelete: (temaId: string) => void;
  onToggleCompletado: (temaId: string, completado: boolean) => void;
  isUpdating: boolean;
}

export interface EditModalProps extends BaseModalProps {
  onSave: (data: any) => void;
}

// ============================================================================
// TIPOS DE DIAGNÓSTICO
// ============================================================================

export interface DiagnosticInfo {
  supabaseConnection: boolean;
  userAuthenticated: boolean;
  geminiApiKey: boolean;
  conversationsCount: number;
  documentsCount: number;
  lastError: string | null;
}
