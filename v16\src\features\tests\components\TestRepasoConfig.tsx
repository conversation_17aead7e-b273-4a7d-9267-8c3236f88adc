import React, { useState, useEffect } from 'react';
import { Test, PreguntaTest } from '@/lib/supabase/supabaseClient';
import { obtenerTests, obtenerPreguntasTestCount, obtenerPreguntasRepaso } from '@/lib/supabase/testsService';
import { FiPlay, FiRefreshCw, FiCheckSquare, FiX } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

interface TestConContador extends Test {
  numPreguntas: number;
}

interface ConfiguracionTest {
  testId: string;
  cantidad: number;
  maxPreguntas: number;
}

interface TestRepasoConfigProps {
  onIniciarRepaso: (preguntas: PreguntaTest[], configuracion: ConfiguracionTest[]) => void;
  onCancelar: () => void;
}

export default function TestRepasoConfig({ onIniciarRepaso, onCancelar }: TestRepasoConfigProps) {
  const [tests, setTests] = useState<TestConContador[]>([]);
  const [configuracion, setConfiguracion] = useState<ConfiguracionTest[]>([]);
  const [cargando, setCargando] = useState(false);
  const [generandoRepaso, setGenerandoRepaso] = useState(false);

  useEffect(() => {
    cargarTests();
  }, []);

  const cargarTests = async () => {
    setCargando(true);
    try {
      const testsData = await obtenerTests();
      
      // Obtener el número de preguntas para cada test
      const testsConContador: TestConContador[] = [];
      for (const test of testsData) {
        const numPreguntas = await obtenerPreguntasTestCount(test.id);
        if (numPreguntas > 0) { // Solo incluir tests que tengan preguntas
          testsConContador.push({
            ...test,
            numPreguntas
          });
        }
      }

      setTests(testsConContador);
      
      // Inicializar configuración
      const configInicial = testsConContador.map(test => ({
        testId: test.id,
        cantidad: 0,
        maxPreguntas: test.numPreguntas
      }));
      setConfiguracion(configInicial);
      
    } catch (error) {
      console.error('Error al cargar tests:', error);
      toast.error('No se pudieron cargar los tests.');
    } finally {
      setCargando(false);
    }
  };

  const actualizarCantidad = (testId: string, cantidad: number) => {
    setConfiguracion(prev => 
      prev.map(config => 
        config.testId === testId 
          ? { ...config, cantidad: Math.max(0, Math.min(cantidad, config.maxPreguntas)) }
          : config
      )
    );
  };

  const seleccionarTodos = () => {
    setConfiguracion(prev => 
      prev.map(config => ({ ...config, cantidad: config.maxPreguntas }))
    );
  };

  const limpiarTodos = () => {
    setConfiguracion(prev => 
      prev.map(config => ({ ...config, cantidad: 0 }))
    );
  };

  const handleIniciarRepaso = async () => {
    const configuracionValida = configuracion.filter(config => config.cantidad > 0);
    
    if (configuracionValida.length === 0) {
      toast.error('Selecciona al menos una pregunta de algún test.');
      return;
    }

    setGenerandoRepaso(true);
    try {
      const preguntas = await obtenerPreguntasRepaso(configuracionValida);
      
      if (preguntas.length === 0) {
        toast.error('No se pudieron obtener preguntas para el repaso.');
        return;
      }

      const totalSeleccionado = configuracionValida.reduce((sum, config) => sum + config.cantidad, 0);
      toast.success(`Test de repaso creado con ${preguntas.length} preguntas de ${configuracionValida.length} test(s)`);
      
      onIniciarRepaso(preguntas, configuracionValida);
    } catch (error) {
      console.error('Error al generar test de repaso:', error);
      toast.error('Error al generar el test de repaso.');
    } finally {
      setGenerandoRepaso(false);
    }
  };

  const totalPreguntas = configuracion.reduce((sum, config) => sum + config.cantidad, 0);
  const testsSeleccionados = configuracion.filter(config => config.cantidad > 0).length;

  if (cargando) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-600">Cargando tests...</span>
      </div>
    );
  }

  if (tests.length === 0) {
    return (
      <div className="text-center py-8">
        <FiCheckSquare className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No hay tests disponibles</h3>
        <p className="mt-1 text-sm text-gray-500">
          Primero necesitas crear algunos tests con preguntas para poder hacer un repaso.
        </p>
        <button
          onClick={onCancelar}
          className="mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
        >
          Volver
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold">Configurar Test de Repaso</h2>
          <p className="text-gray-600">
            Selecciona cuántas preguntas quieres de cada test para crear tu repaso personalizado.
          </p>
        </div>
        <button
          onClick={onCancelar}
          className="p-2 text-gray-500 hover:text-gray-700"
          title="Cancelar"
        >
          <FiX className="h-6 w-6" />
        </button>
      </div>

      {/* Controles globales */}
      <div className="flex justify-between items-center bg-gray-50 p-4 rounded-lg">
        <div className="flex gap-2">
          <button
            onClick={seleccionarTodos}
            className="px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200"
          >
            Seleccionar todas
          </button>
          <button
            onClick={limpiarTodos}
            className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
          >
            Limpiar todo
          </button>
        </div>
        <div className="text-sm text-gray-600">
          <strong>{totalPreguntas}</strong> preguntas de <strong>{testsSeleccionados}</strong> test(s)
        </div>
      </div>

      {/* Lista de tests */}
      <div className="space-y-4">
        {tests.map(test => {
          const config = configuracion.find(c => c.testId === test.id);
          if (!config) return null;

          return (
            <div key={test.id} className="border rounded-lg p-4 bg-white">
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{test.titulo}</h3>
                  {test.descripcion && (
                    <p className="text-sm text-gray-500 mt-1">{test.descripcion}</p>
                  )}
                  <p className="text-xs text-gray-400 mt-1">
                    Creado: {new Date(test.creado_en).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-sm text-gray-500">
                  {test.numPreguntas} pregunta{test.numPreguntas !== 1 ? 's' : ''} disponible{test.numPreguntas !== 1 ? 's' : ''}
                </div>
              </div>

              <div className="flex items-center gap-4">
                <label className="text-sm font-medium text-gray-700">
                  Preguntas a incluir:
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="range"
                    min="0"
                    max={config.maxPreguntas}
                    value={config.cantidad}
                    onChange={(e) => actualizarCantidad(test.id, parseInt(e.target.value))}
                    className="flex-1"
                  />
                  <input
                    type="number"
                    min="0"
                    max={config.maxPreguntas}
                    value={config.cantidad}
                    onChange={(e) => actualizarCantidad(test.id, parseInt(e.target.value) || 0)}
                    className="w-16 px-2 py-1 border rounded text-center text-sm"
                  />
                  <span className="text-sm text-gray-500">/ {config.maxPreguntas}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Botón de iniciar repaso */}
      <div className="flex justify-center pt-4">
        <button
          onClick={handleIniciarRepaso}
          disabled={totalPreguntas === 0 || generandoRepaso}
          className={`flex items-center px-6 py-3 rounded-lg font-medium ${
            totalPreguntas === 0 || generandoRepaso
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-indigo-600 text-white hover:bg-indigo-700'
          }`}
        >
          {generandoRepaso ? (
            <>
              <FiRefreshCw className="mr-2 h-5 w-5 animate-spin" />
              Generando repaso...
            </>
          ) : (
            <>
              <FiPlay className="mr-2 h-5 w-5" />
              Iniciar Repaso ({totalPreguntas} preguntas)
            </>
          )}
        </button>
      </div>
    </div>
  );
}
