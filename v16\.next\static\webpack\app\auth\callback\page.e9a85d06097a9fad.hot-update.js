"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n// src/app/auth/callback/page.tsx\n// VERSIÓN MODIFICADA CON MANEJO MEJORADO Y FLAG PARA SETUP DE CONTRASEÑA\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n // Añadir useRef\n\n\nfunction AuthCallbackContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Procesando autenticación, por favor espera...');\n    const timeoutIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // Usar ref para el ID del timeout\n    // Función para añadir logs a la consola\n    const log = (msg, data)=>{\n        const timestamp = new Date().toISOString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(msg);\n        console.log(logEntry, data || '');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthCallbackContent.useEffect\": ()=>{\n            log('=== [AUTH-CALLBACK-PAGE] AuthCallbackContent useEffect INICIADO ===');\n            // Log inicial de la URL para ver qué llega\n            log('🌐 [AUTH-CALLBACK-PAGE] URL Completa en el cliente:', window.location.href);\n            log('📋 [AUTH-CALLBACK-PAGE] Query Params (searchParams):', Object.fromEntries(searchParams.entries()));\n            log('🔍 [AUTH-CALLBACK-PAGE] Verificando si llegamos desde el servidor o directamente...');\n            const errorDescription = searchParams.get('error_description');\n            const errorCode = searchParams.get('error_code');\n            const errorParam = searchParams.get('error');\n            if (errorDescription || errorCode || errorParam) {\n                let friendlyMessage = decodeURIComponent(errorDescription || errorParam || 'Error desconocido');\n                if (errorCode === 'user_already_invited' || errorDescription && errorDescription.includes('User already invited')) {\n                    friendlyMessage = 'Ya se ha enviado una invitación a este correo. Por favor, revisa tu bandeja de entrada (y spam). Si no la encuentras, puedes intentar recuperar tu contraseña desde la página de login.';\n                } else if (errorCode === 'token_expired_or_invalid' || errorDescription && (errorDescription.includes('invalid token') || errorDescription.includes('expired'))) {\n                    friendlyMessage = 'El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta registrarte o iniciar sesión de nuevo.';\n                } else if (friendlyMessage.includes('Código de autenticación no encontrado')) {\n                    friendlyMessage = 'El enlace de confirmación no es válido. Por favor, verifica que hayas copiado la URL completa del email.';\n                } else if (friendlyMessage.includes('No se pudo completar la autenticación')) {\n                    friendlyMessage = 'No se pudo completar la verificación de tu email. Por favor, intenta usar el enlace del email nuevamente.';\n                }\n                log('❌ Error explícito detectado en la URL.', {\n                    error: friendlyMessage,\n                    code: errorCode,\n                    error_param: errorParam\n                });\n                setStatus('error');\n                setMessage(friendlyMessage);\n                if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current); // Limpiar timeout si hay error\n                return;\n            }\n            const supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            log('Cliente Supabase del navegador inicializado.');\n            // Verificar si hay código en la URL y dar tiempo a Supabase para procesarlo\n            const code = searchParams.get('code');\n            if (code) {\n                log('🔑 [AUTH-CALLBACK-PAGE] Código encontrado en URL:', code.substring(0, 10) + '...');\n                log('⏳ [AUTH-CALLBACK-PAGE] Esperando que Supabase procese automáticamente el código...');\n                // Dar tiempo a Supabase para procesar el código automáticamente\n                setTimeout({\n                    \"AuthCallbackContent.useEffect\": ()=>{\n                        supabase.auth.getSession().then({\n                            \"AuthCallbackContent.useEffect\": (param)=>{\n                                let { data: { session } } = param;\n                                if (session) {\n                                    log('✅ [AUTH-CALLBACK-PAGE] Sesión encontrada después del procesamiento automático');\n                                // El listener onAuthStateChange debería detectar esto\n                                } else {\n                                    log('⚠️ [AUTH-CALLBACK-PAGE] No hay sesión después del procesamiento automático, verificando...');\n                                // Si no hay sesión, el listener INITIAL_SESSION lo manejará\n                                }\n                            }\n                        }[\"AuthCallbackContent.useEffect\"]);\n                    }\n                }[\"AuthCallbackContent.useEffect\"], 1000); // Dar 1 segundo para que Supabase procese\n            }\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthCallbackContent.useEffect\": async (event, session)=>{\n                    var _session_user, _session_user1;\n                    log(\"EVENTO onAuthStateChange RECIBIDO: \".concat(event), {\n                        hasSession: !!session,\n                        userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                        userMetadata: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.user_metadata\n                    });\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current); // Limpiar el timeout si se maneja un evento\n                        timeoutIdRef.current = null;\n                    }\n                    if (event === 'PASSWORD_RECOVERY') {\n                        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');\n                        setStatus('success');\n                        setMessage('Sesión de recuperación lista. Redirigiendo para establecer contraseña...');\n                        router.push('/auth/reset-password');\n                    // ===== MODIFICACIÓN START (Lógica para requires_password_setup en SIGNED_IN) =====\n                    } else if (event === 'SIGNED_IN') {\n                        log('✅ Evento SIGNED_IN detectado.');\n                        if (session === null || session === void 0 ? void 0 : session.user) {\n                            var _session_user_user_metadata;\n                            // Verificar si el usuario necesita configurar su contraseña\n                            const userRequiresPasswordSetup = ((_session_user_user_metadata = session.user.user_metadata) === null || _session_user_user_metadata === void 0 ? void 0 : _session_user_user_metadata.requires_password_setup) === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Usuario necesita establecer contraseña inicial. Redirigiendo a /auth/reset-password...');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                // /auth/reset-password manejará la sesión actual y permitirá actualizar la contraseña\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Usuario ya tiene contraseña o no requiere setup. Redirigiendo a /app...');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('⚠️ Evento SIGNED_IN sin sesión completa. Comportamiento inesperado.');\n                            setStatus('error');\n                            setMessage('Error inesperado durante el inicio de sesión. Por favor, inténtalo de nuevo.');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'USER_UPDATED' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        var _session_user_user_metadata1;\n                        // Este evento puede ser útil si la confirmación de email ocurre y luego se necesita setear contraseña.\n                        log('✅ Evento USER_UPDATED con sesión.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en USER_UPDATED) =====\n                        const userRequiresPasswordSetup = ((_session_user_user_metadata1 = session.user.user_metadata) === null || _session_user_user_metadata1 === void 0 ? void 0 : _session_user_user_metadata1.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Usuario actualizado necesita establecer contraseña. Redirigiendo a /auth/reset-password...');\n                            setStatus('success');\n                            setMessage('Actualización de cuenta completada. Crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Cuenta actualizada. Redirigiendo a /app...');\n                            setStatus('success');\n                            setMessage('Cuenta actualizada. Redirigiendo...');\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'INITIAL_SESSION' && !session) {\n                        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos o timeout.');\n                    // No hacer nada, esperar al timeout si no llegan más eventos.\n                    } else if (event === 'INITIAL_SESSION' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        var _session_user_user_metadata2;\n                        // Se encontró una sesión inicial, puede ser de una pestaña anterior o un token válido procesado rápido.\n                        log('✅ Evento INITIAL_SESSION con sesión. Procediendo como SIGNED_IN.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en INITIAL_SESSION) =====\n                        const userRequiresPasswordSetup = ((_session_user_user_metadata2 = session.user.user_metadata) === null || _session_user_user_metadata2 === void 0 ? void 0 : _session_user_user_metadata2.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            router.push('/auth/reset-password');\n                        } else {\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"]);\n            log('Listener onAuthStateChange configurado.');\n            // Verificación inmediata de la sesión (por si ya está autenticado)\n            const checkCurrentSession = {\n                \"AuthCallbackContent.useEffect.checkCurrentSession\": async ()=>{\n                    const { data: { session: currentSession } } = await supabase.auth.getSession();\n                    if ((currentSession === null || currentSession === void 0 ? void 0 : currentSession.user) && status === 'loading') {\n                        var _currentSession_user_user_metadata;\n                        log('✅ Sesión ya existente encontrada inmediatamente.');\n                        const userRequiresPasswordSetup = ((_currentSession_user_user_metadata = currentSession.user.user_metadata) === null || _currentSession_user_user_metadata === void 0 ? void 0 : _currentSession_user_user_metadata.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Redirigiendo a /auth/reset-password (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Redirigiendo a /app (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Autenticación exitosa! Redirigiendo...');\n                            router.push('/app');\n                        }\n                        if (timeoutIdRef.current) {\n                            clearTimeout(timeoutIdRef.current);\n                            timeoutIdRef.current = null;\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect.checkCurrentSession\"];\n            // Ejecutar verificación inmediata\n            checkCurrentSession();\n            // Timeout reducido para casos donde no se recibe el evento esperado\n            timeoutIdRef.current = setTimeout({\n                \"AuthCallbackContent.useEffect\": async ()=>{\n                    if (status === 'loading') {\n                        const { data: { session: finalCheckSession }, error: finalCheckError } = await supabase.auth.getSession();\n                        if (finalCheckSession === null || finalCheckSession === void 0 ? void 0 : finalCheckSession.user) {\n                            var _finalCheckSession_user_user_metadata;\n                            log('✅ Sesión encontrada en la verificación final del timeout.');\n                            const userRequiresPasswordSetup = ((_finalCheckSession_user_user_metadata = finalCheckSession.user.user_metadata) === null || _finalCheckSession_user_user_metadata === void 0 ? void 0 : _finalCheckSession_user_user_metadata.requires_password_setup) === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Redirigiendo a /auth/reset-password desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Redirigiendo a /app desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('❌ TIMEOUT (5s): No se recibió un evento de autenticación válido y no hay sesión activa.');\n                            log('Detalles del error en la verificación final del timeout:', finalCheckError === null || finalCheckError === void 0 ? void 0 : finalCheckError.message);\n                            setStatus('error');\n                            setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Si el problema persiste, intenta registrarte de nuevo o contacta a soporte.');\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"], 5000); // Reducido a 5 segundos\n            return ({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    log('--- AuthCallbackContent useEffect LIMPIEZA ---');\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    log('Suscripción a onAuthStateChange eliminada.');\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                        log('Temporizador de timeout limpiado.');\n                    }\n                }\n            })[\"AuthCallbackContent.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthCallbackContent.useEffect\"], [\n        router,\n        searchParams\n    ]);\n    // ... (resto del componente JSX sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Procesando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"\\xa1\\xc9xito!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Error de Autenticaci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/login'),\n                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                                children: \"Ir a Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackContent, \"l3Y1eluPkBdI6/1F5+JLVBI8lNM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AuthCallbackContent;\nfunction AuthCallbackPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Procesando autenticaci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthCallbackContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AuthCallbackPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AuthCallbackContent\");\n$RefreshReg$(_c1, \"AuthCallbackPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback/page.tsx\n"));

/***/ })

});