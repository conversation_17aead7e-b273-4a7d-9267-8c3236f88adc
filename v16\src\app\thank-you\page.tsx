// ===== Archivo: src\app\thank-you\page.tsx =====
// src/app/thank-you/page.tsx
'use client';

import React, { Suspense, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { getPlanById } from '@/lib/stripe/plans';
import Link from 'next/link';
import {
    FiCheckCircle,
    FiAlertTriangle,
    FiMail,
    FiLoader
} from 'react-icons/fi';

function ThankYouContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const planIdParam = searchParams.get('plan') || 'free';
  const sessionId = searchParams.get('session_id');
  const emailSent = searchParams.get('email_sent') === 'true';
  const paymentConfirmed = searchParams.get('payment') === 'true';

  const planDetails = getPlanById(planIdParam);

  // Determinar qué mostrar basado en los parámetros
  const isPaymentFlow = sessionId && sessionId !== 'undefined';
  const isFreeRegistration = emailSent && planIdParam === 'free';

  // ELIMINADO: Ya no redirigimos automáticamente a payment-pending
  // La página thank-you ahora es el destino final después del pago

  // Renderizado para registro gratuito
  if (isFreeRegistration) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-xl">
          <div className="bg-white py-8 px-6 shadow-xl sm:rounded-lg sm:px-10">
            <div className="text-center">
              <FiMail className="mx-auto h-12 w-12 text-blue-600 mb-4" />
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-3">
                ¡Registro Exitoso!
              </h2>
              <p className="text-md text-gray-600 mb-6">
                Tu cuenta gratuita de <strong>{planDetails?.name || 'OposicionesIA'}</strong> ha sido creada exitosamente.
              </p>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-blue-800 mb-2">📧 Confirma tu Email</h3>
                <p className="text-blue-700 text-sm mb-3">
                  Hemos enviado un email de confirmación a tu dirección de correo.
                </p>
                <p className="text-blue-700 text-sm">
                  <strong>Haz clic en el enlace del email</strong> para activar tu cuenta y poder iniciar sesión.
                </p>
              </div>

              <p className="text-sm text-gray-600 mb-6">
                Si no recibes el email en unos minutos, revisa tu carpeta de spam.
              </p>

              <Link
                href="/auth/login"
                className="inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                Ir a Iniciar Sesión
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Renderizado para flujo de pago
  if (isPaymentFlow) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-xl">
          <div className="bg-white py-10 px-6 shadow-xl sm:rounded-lg sm:px-10">
            <div className="text-center">
              <FiCheckCircle className="mx-auto h-16 w-16 text-green-500 mb-5" />
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                ¡Pago Confirmado!
              </h2>
              <p className="text-md text-gray-700 mb-6">
                Tu pago para el plan <strong>{planDetails?.name || 'seleccionado'}</strong> ha sido procesado exitosamente.
              </p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <p className="text-green-800 text-sm">
                  <strong>✅ Tu cuenta se está activando</strong><br/>
                  En unos momentos, tu cuenta estará completamente activada.
                  Mientras tanto, puedes intentar iniciar sesión.
                </p>
              </div>
              <p className="text-sm text-gray-600 mb-8">
                Si tienes algún problema para iniciar sesión, espera unos minutos y vuelve a intentarlo.
              </p>
              <Link
                href="/auth/login"
                className="inline-flex justify-center py-3 px-8 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                Ir a Iniciar Sesión
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Fallback para casos no manejados
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
      <div className="text-center">
        <FiAlertTriangle className="mx-auto h-12 w-12 text-orange-500 mb-4" />
        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          Página de Agradecimiento
        </h2>
        <p className="text-gray-600 mb-6">
          No se encontraron parámetros válidos para mostrar el contenido apropiado.
        </p>
        <Link
          href="/"
          className="inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
        >
          Volver al Inicio
        </Link>
      </div>
    </div>
  );
}

export default function ThankYouPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
        <FiLoader className="animate-spin h-12 w-12 text-blue-600" />
        <p className="mt-4 text-gray-600">Cargando...</p>
      </div>
    }>
      <ThankYouContent />
    </Suspense>
  );
}