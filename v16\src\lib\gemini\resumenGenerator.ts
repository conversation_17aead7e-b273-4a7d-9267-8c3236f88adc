import { prepararDocumentos } from './geminiClient';
import { PROMPT_RESUMENES } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';

/**
 * Genera un resumen de un documento usando IA
 */
export async function generarResumen(
  documento: { titulo: string; contenido: string; categoria?: string; numero_tema?: number },
  instrucciones?: string
): Promise<string> {
  try {
    // Validar entrada
    if (!documento || !documento.contenido) {
      throw new Error("No se ha proporcionado un documento válido para generar el resumen.");
    }

    // Validar que el contenido no esté vacío
    if (documento.contenido.trim().length === 0) {
      throw new Error("El contenido del documento está vacío.");
    }

    // Preparar el contenido del documento usando el mismo patrón que otros servicios
    const documentos = [documento];
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se pudo preparar el contenido del documento.");
    }

    // Validar y limpiar instrucciones
    const instruccionesLimpias = instrucciones?.trim() || 'Crea un resumen completo y estructurado del tema proporcionado.';

    // Construir el prompt final siguiendo el patrón de otros servicios
    let finalPrompt = PROMPT_RESUMENES.replace('{titulo_del_tema}', documento.titulo || 'Tema sin título');
    finalPrompt = finalPrompt.replace('{documento}', contenidoDocumentos);
    finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

    // Obtener configuración específica para resúmenes
    const config = getOpenAIConfig('RESUMENES');

    console.log(`📄 Generando resumen con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

    // Generar el resumen usando OpenAI con la configuración correcta
    const messages = [{ role: 'user' as const, content: finalPrompt }];
    const responseText = await llamarOpenAI(messages, {
      ...config,
      activityName: 'Generación de Resumen'
    });

    // Validar que la respuesta no esté vacía
    if (!responseText || responseText.trim().length === 0) {
      throw new Error("La IA no generó ningún contenido para el resumen.");
    }

    // Limpiar y formatear la respuesta
    let resumenContent = responseText.trim();

    // Asegurar que el resumen tenga un formato mínimo
    if (resumenContent.length < 100) {
      throw new Error("El resumen generado es demasiado corto. Por favor, inténtalo de nuevo.");
    }

    console.log('✅ Resumen generado exitosamente');
    return resumenContent;

  } catch (error) {
    console.error('Error al generar resumen:', error);

    // Proporcionar un mensaje de error más descriptivo si es posible
    if (error instanceof Error) {
      throw new Error(`Error al generar el resumen: ${error.message}`);
    }

    throw new Error("Ha ocurrido un error inesperado al generar el resumen.");
  }
}
