"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n// ===== Archivo: src\\app\\auth\\callback\\page.tsx (CORREGIDO Y SIMPLIFICADO) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AuthCallbackContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Procesando autenticación, por favor espera...');\n    const [logMessages, setLogMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const timeoutIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const log = (msg, data)=>{\n        const timestamp = new Date().toISOString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(msg);\n        console.log(logEntry, data || '');\n        setLogMessages((prev)=>[\n                ...prev,\n                logEntry\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthCallbackContent.useEffect\": ()=>{\n            log('--- AuthCallbackContent useEffect INICIADO ---');\n            log('URL Completa en el cliente:', window.location.href);\n            const errorDescription = searchParams.get('error_description');\n            if (errorDescription) {\n                log('❌ Error explícito en la URL.', {\n                    error: errorDescription\n                });\n                setStatus('error');\n                setMessage(decodeURIComponent(errorDescription));\n                return;\n            }\n            const supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            log('Cliente Supabase del navegador inicializado.');\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthCallbackContent.useEffect\": async (event, session)=>{\n                    log(\"EVENTO onAuthStateChange RECIBIDO: \".concat(event), {\n                        hasSession: !!session\n                    });\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                        timeoutIdRef.current = null;\n                    }\n                    if (event === 'SIGNED_IN') {\n                        log('✅ Evento SIGNED_IN detectado. Redirigiendo a /app...');\n                        setStatus('success');\n                        setMessage('¡Autenticación exitosa! Redirigiendo...');\n                        router.push('/app');\n                    } else if (event === 'PASSWORD_RECOVERY') {\n                        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');\n                        setStatus('success');\n                        setMessage('Sesión de recuperación lista. Redirigiendo...');\n                        router.push('/auth/reset-password');\n                    } else if (event === 'USER_UPDATED' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        log('✅ Evento USER_UPDATED con sesión. Redirigiendo a /app...');\n                        setStatus('success');\n                        setMessage('Cuenta actualizada. Redirigiendo...');\n                        router.push('/app');\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"]);\n            log('Listener onAuthStateChange configurado. Esperando evento...');\n            // Iniciar un timeout de seguridad más largo (10-15s) para manejar casos raros\n            timeoutIdRef.current = setTimeout({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    if (status === 'loading') {\n                        log('❌ TIMEOUT (10s): No se recibió un evento de autenticación válido.');\n                        setStatus('error');\n                        setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta de nuevo.');\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"], 10000); // 10 segundos\n            return ({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    log('--- AuthCallbackContent useEffect LIMPIEZA ---');\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                    }\n                }\n            })[\"AuthCallbackContent.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthCallbackContent.useEffect\"], [\n        router\n    ]);\n    // ... (resto del componente JSX sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Procesando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"\\xa1\\xc9xito!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Error de Autenticaci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/login'),\n                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                                children: \"Ir a Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 pt-4 border-t border-gray-200 text-left bg-gray-50 p-3 rounded max-h-48 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-700 mb-2\",\n                            children: \"Registro de Eventos:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 space-y-1\",\n                            children: logMessages.map((logMsg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: logMsg\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 53\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackContent, \"fOlhbpKiY/OUV4HDYod1OUHDlWc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AuthCallbackContent;\nfunction AuthCallbackPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Procesando autenticaci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthCallbackContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AuthCallbackPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AuthCallbackContent\");\n$RefreshReg$(_c1, \"AuthCallbackPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback/page.tsx\n"));

/***/ })

});