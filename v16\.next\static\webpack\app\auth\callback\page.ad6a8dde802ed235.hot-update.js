"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/callback/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n// src/app/auth/callback/page.tsx\n// VERSIÓN MODIFICADA CON MANEJO MEJORADO Y FLAG PARA SETUP DE CONTRASEÑA\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n // Añadir useRef\n\n // Usar tu cliente configurado\nfunction AuthCallbackContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Procesando autenticación, por favor espera...');\n    const [logMessages, setLogMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const timeoutIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // Usar ref para el ID del timeout\n    // Función para añadir logs al estado y a la consola\n    const log = (msg, data)=>{\n        const timestamp = new Date().toISOString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(msg);\n        console.log(logEntry, data || '');\n        setLogMessages((prev)=>[\n                ...prev,\n                logEntry\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthCallbackContent.useEffect\": ()=>{\n            log('--- AuthCallbackContent useEffect INICIADO ---');\n            // Log inicial de la URL para ver qué llega\n            log('URL Completa en el cliente:', window.location.href);\n            log('Query Params (searchParams):', Object.fromEntries(searchParams.entries()));\n            const errorDescription = searchParams.get('error_description');\n            const errorCode = searchParams.get('error_code');\n            const errorParam = searchParams.get('error');\n            if (errorDescription || errorCode || errorParam) {\n                let friendlyMessage = decodeURIComponent(errorDescription || errorParam || 'Error desconocido');\n                if (errorCode === 'user_already_invited' || errorDescription && errorDescription.includes('User already invited')) {\n                    friendlyMessage = 'Ya se ha enviado una invitación a este correo. Por favor, revisa tu bandeja de entrada (y spam). Si no la encuentras, puedes intentar recuperar tu contraseña desde la página de login.';\n                } else if (errorCode === 'token_expired_or_invalid' || errorDescription && (errorDescription.includes('invalid token') || errorDescription.includes('expired'))) {\n                    friendlyMessage = 'El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta registrarte o iniciar sesión de nuevo.';\n                } else if (friendlyMessage.includes('Código de autenticación no encontrado')) {\n                    friendlyMessage = 'El enlace de confirmación no es válido. Por favor, verifica que hayas copiado la URL completa del email.';\n                } else if (friendlyMessage.includes('No se pudo completar la autenticación')) {\n                    friendlyMessage = 'No se pudo completar la verificación de tu email. Por favor, intenta usar el enlace del email nuevamente.';\n                }\n                log('❌ Error explícito detectado en la URL.', {\n                    error: friendlyMessage,\n                    code: errorCode,\n                    error_param: errorParam\n                });\n                setStatus('error');\n                setMessage(friendlyMessage);\n                if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current); // Limpiar timeout si hay error\n                return;\n            }\n            const supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.createClient)();\n            log('Cliente Supabase del navegador inicializado.');\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthCallbackContent.useEffect\": async (event, session)=>{\n                    var _session_user, _session_user1;\n                    log(\"EVENTO onAuthStateChange RECIBIDO: \".concat(event), {\n                        hasSession: !!session,\n                        userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                        userMetadata: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.user_metadata\n                    });\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current); // Limpiar el timeout si se maneja un evento\n                        timeoutIdRef.current = null;\n                    }\n                    if (event === 'PASSWORD_RECOVERY') {\n                        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');\n                        setStatus('success');\n                        setMessage('Sesión de recuperación lista. Redirigiendo para establecer contraseña...');\n                        router.push('/auth/reset-password');\n                    // ===== MODIFICACIÓN START (Lógica para requires_password_setup en SIGNED_IN) =====\n                    } else if (event === 'SIGNED_IN') {\n                        log('✅ Evento SIGNED_IN detectado.');\n                        if (session === null || session === void 0 ? void 0 : session.user) {\n                            var _session_user_user_metadata;\n                            // Verificar si el usuario necesita configurar su contraseña\n                            const userRequiresPasswordSetup = ((_session_user_user_metadata = session.user.user_metadata) === null || _session_user_user_metadata === void 0 ? void 0 : _session_user_user_metadata.requires_password_setup) === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Usuario necesita establecer contraseña inicial. Redirigiendo a /auth/reset-password...');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                // /auth/reset-password manejará la sesión actual y permitirá actualizar la contraseña\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Usuario ya tiene contraseña o no requiere setup. Redirigiendo a /app...');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('⚠️ Evento SIGNED_IN sin sesión completa. Comportamiento inesperado.');\n                            setStatus('error');\n                            setMessage('Error inesperado durante el inicio de sesión. Por favor, inténtalo de nuevo.');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'USER_UPDATED' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        var _session_user_user_metadata1;\n                        // Este evento puede ser útil si la confirmación de email ocurre y luego se necesita setear contraseña.\n                        log('✅ Evento USER_UPDATED con sesión.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en USER_UPDATED) =====\n                        const userRequiresPasswordSetup = ((_session_user_user_metadata1 = session.user.user_metadata) === null || _session_user_user_metadata1 === void 0 ? void 0 : _session_user_user_metadata1.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Usuario actualizado necesita establecer contraseña. Redirigiendo a /auth/reset-password...');\n                            setStatus('success');\n                            setMessage('Actualización de cuenta completada. Crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Cuenta actualizada. Redirigiendo a /app...');\n                            setStatus('success');\n                            setMessage('Cuenta actualizada. Redirigiendo...');\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    } else if (event === 'INITIAL_SESSION' && !session) {\n                        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos o timeout.');\n                    // No hacer nada, esperar al timeout si no llegan más eventos.\n                    } else if (event === 'INITIAL_SESSION' && (session === null || session === void 0 ? void 0 : session.user)) {\n                        var _session_user_user_metadata2;\n                        // Se encontró una sesión inicial, puede ser de una pestaña anterior o un token válido procesado rápido.\n                        log('✅ Evento INITIAL_SESSION con sesión. Procediendo como SIGNED_IN.');\n                        // ===== MODIFICACIÓN START (Lógica para requires_password_setup en INITIAL_SESSION) =====\n                        const userRequiresPasswordSetup = ((_session_user_user_metadata2 = session.user.user_metadata) === null || _session_user_user_metadata2 === void 0 ? void 0 : _session_user_user_metadata2.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            router.push('/auth/reset-password');\n                        } else {\n                            router.push('/app');\n                        }\n                    // ===== MODIFICACIÓN END =====\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"]);\n            log('Listener onAuthStateChange configurado.');\n            // Verificación inmediata de la sesión (por si ya está autenticado)\n            const checkCurrentSession = {\n                \"AuthCallbackContent.useEffect.checkCurrentSession\": async ()=>{\n                    const { data: { session: currentSession } } = await supabase.auth.getSession();\n                    if ((currentSession === null || currentSession === void 0 ? void 0 : currentSession.user) && status === 'loading') {\n                        var _currentSession_user_user_metadata;\n                        log('✅ Sesión ya existente encontrada inmediatamente.');\n                        const userRequiresPasswordSetup = ((_currentSession_user_user_metadata = currentSession.user.user_metadata) === null || _currentSession_user_user_metadata === void 0 ? void 0 : _currentSession_user_user_metadata.requires_password_setup) === true;\n                        if (userRequiresPasswordSetup) {\n                            log('👤 Redirigiendo a /auth/reset-password (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                            router.push('/auth/reset-password');\n                        } else {\n                            log('🚀 Redirigiendo a /app (sesión existente).');\n                            setStatus('success');\n                            setMessage('¡Autenticación exitosa! Redirigiendo...');\n                            router.push('/app');\n                        }\n                        if (timeoutIdRef.current) {\n                            clearTimeout(timeoutIdRef.current);\n                            timeoutIdRef.current = null;\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect.checkCurrentSession\"];\n            // Ejecutar verificación inmediata\n            checkCurrentSession();\n            // Timeout reducido para casos donde no se recibe el evento esperado\n            timeoutIdRef.current = setTimeout({\n                \"AuthCallbackContent.useEffect\": async ()=>{\n                    if (status === 'loading') {\n                        const { data: { session: finalCheckSession }, error: finalCheckError } = await supabase.auth.getSession();\n                        if (finalCheckSession === null || finalCheckSession === void 0 ? void 0 : finalCheckSession.user) {\n                            var _finalCheckSession_user_user_metadata;\n                            log('✅ Sesión encontrada en la verificación final del timeout.');\n                            const userRequiresPasswordSetup = ((_finalCheckSession_user_user_metadata = finalCheckSession.user.user_metadata) === null || _finalCheckSession_user_user_metadata === void 0 ? void 0 : _finalCheckSession_user_user_metadata.requires_password_setup) === true;\n                            if (userRequiresPasswordSetup) {\n                                log('👤 Redirigiendo a /auth/reset-password desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Cuenta activada! Ahora, crea tu contraseña...');\n                                router.push('/auth/reset-password');\n                            } else {\n                                log('🚀 Redirigiendo a /app desde timeout.');\n                                setStatus('success');\n                                setMessage('¡Autenticación exitosa! Redirigiendo...');\n                                router.push('/app');\n                            }\n                        } else {\n                            log('❌ TIMEOUT (5s): No se recibió un evento de autenticación válido y no hay sesión activa.');\n                            log('Detalles del error en la verificación final del timeout:', finalCheckError === null || finalCheckError === void 0 ? void 0 : finalCheckError.message);\n                            setStatus('error');\n                            setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Si el problema persiste, intenta registrarte de nuevo o contacta a soporte.');\n                        }\n                    }\n                }\n            }[\"AuthCallbackContent.useEffect\"], 5000); // Reducido a 5 segundos\n            return ({\n                \"AuthCallbackContent.useEffect\": ()=>{\n                    log('--- AuthCallbackContent useEffect LIMPIEZA ---');\n                    authListener === null || authListener === void 0 ? void 0 : authListener.subscription.unsubscribe();\n                    log('Suscripción a onAuthStateChange eliminada.');\n                    if (timeoutIdRef.current) {\n                        clearTimeout(timeoutIdRef.current);\n                        log('Temporizador de timeout limpiado.');\n                    }\n                }\n            })[\"AuthCallbackContent.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthCallbackContent.useEffect\"], [\n        router,\n        searchParams\n    ]);\n    // ... (resto del componente JSX sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Procesando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"\\xa1\\xc9xito!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                            children: \"Error de Autenticaci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/login'),\n                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\",\n                                children: \"Ir a Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackContent, \"fOlhbpKiY/OUV4HDYod1OUHDlWc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AuthCallbackContent;\nfunction AuthCallbackPage() {\n    // ... (código Suspense sin cambios)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Procesando autenticaci\\xf3n...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthCallbackContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 264,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 255,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AuthCallbackPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AuthCallbackContent\");\n$RefreshReg$(_c1, \"AuthCallbackPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback/page.tsx\n"));

/***/ })

});