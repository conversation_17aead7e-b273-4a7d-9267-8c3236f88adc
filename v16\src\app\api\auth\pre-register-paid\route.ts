// src/app/api/auth/pre-register-paid/route.ts
// API para pre-registrar usuarios de planes de pago antes del checkout

import { NextRequest, NextResponse } from 'next/server';
import { SupabaseAdminService } from '@/lib/supabase/admin';
import { getPlanConfiguration, getTokenLimitForPlan } from '@/config/plans';

interface PreRegisterRequest {
  email: string;
  password: string;
  customerName?: string;
  planId: string;
}

export async function POST(request: NextRequest) {
  console.log('🚀 [PRE-REGISTER-PAID] Endpoint llamado');

  try {
    const body: PreRegisterRequest = await request.json();
    const { email, password, customerName, planId } = body;

    // --- (Validaciones básicas existentes - sin cambios) ---
    if (!email || !password || !planId || planId === 'free' || password.length < 6) {
      if (!email || !password || !planId) {
        return NextResponse.json({
          error: 'Email, contraseña y plan son requeridos'
        }, { status: 400 });
      }
      if (password.length < 6) {
        return NextResponse.json({
          error: 'La contraseña debe tener al menos 6 caracteres'
        }, { status: 400 });
      }
      if (planId === 'free') {
        return NextResponse.json({
          error: 'Este endpoint es solo para planes de pago'
        }, { status: 400 });
      }
    }

    const planConfig = getPlanConfiguration(planId);
    if (!planConfig) {
      return NextResponse.json({ error: 'Plan no válido' }, { status: 400 });
    }
    // ---------------------------------------------------------

    // ===== INICIO DE LA NUEVA LÓGICA =====

    // 1. VERIFICAR SI EL USUARIO YA EXISTE
    const existingUser = await SupabaseAdminService.getUserByEmail(email);

    let userId: string;

    if (existingUser) {
      // **CASO: USUARIO EXISTENTE (UPGRADE)**
      console.log(`🔄 [PRE-REGISTER-PAID] Usuario existente detectado: ${existingUser.id}. Iniciando flujo de upgrade.`);
      userId = existingUser.id;

      // Obtener el perfil del usuario para verificar su estado actual
      const profile = await SupabaseAdminService.getUserProfile(userId);

      // Opcional: Añadir lógica para evitar que un usuario "Pro" compre un plan "Usuario"
      if (profile && profile.subscription_plan === 'pro' && planId === 'usuario') {
         return NextResponse.json({ error: 'Ya tienes un plan superior. No puedes hacer un downgrade desde aquí.' }, { status: 409 });
      }

      // Actualizar el perfil para reflejar que está pendiente de un nuevo pago
      if (profile) {
        await SupabaseAdminService.upsertUserProfile({
          ...profile,
          security_flags: {
            ...profile.security_flags,
            awaiting_upgrade_payment: true,
            upgrade_target_plan: planId,
            upgrade_initiated_at: new Date().toISOString()
          },
          updated_at: new Date().toISOString()
        });
        console.log(`✅ [PRE-REGISTER-PAID] Perfil del usuario existente marcado como 'awaiting_upgrade_payment'.`);
      } else {
         console.warn(`⚠️ [PRE-REGISTER-PAID] Usuario existe en Auth pero no tiene perfil. Se procederá, pero se creará un perfil nuevo.`);
      }

    } else {
      // **CASO: USUARIO NUEVO (REGISTRO NORMAL)**
      console.log(`✨ [PRE-REGISTER-PAID] Usuario nuevo. Iniciando flujo de pre-registro.`);

      const userData = {
        name: customerName || email.split('@')[0],
        plan: planId,
        payment_verified: false,
        pre_registered: true,
        pre_registration_date: new Date().toISOString(),
        awaiting_payment: true
      };

      // Crear usuario en Supabase sin confirmar email
      const { data, error } = await SupabaseAdminService.createUserWithPassword(
        email,
        password,
        userData,
        false // No enviar email de confirmación todavía
      );

      if (error) {
        // El error de "User already registered" no debería ocurrir aquí gracias a la verificación previa,
        // pero se mantiene por si acaso (condiciones de carrera, etc.)
        if (error.message?.includes('User already registered')) {
          return NextResponse.json({ error: 'Ya existe una cuenta con este email.' }, { status: 409 });
        }
        return NextResponse.json({ error: 'Error creando la cuenta.' }, { status: 500 });
      }

      if (!data?.user) {
        return NextResponse.json({ error: 'Error creando la cuenta' }, { status: 500 });
      }

      userId = data.user.id;
      console.log('✅ Usuario pre-registrado exitosamente:', userId);

      // Crear perfil de usuario en estado pendiente
      await SupabaseAdminService.createUserProfile({
        user_id: userId,
        subscription_plan: planId as 'usuario' | 'pro',
        monthly_token_limit: getTokenLimitForPlan(planId),
        payment_verified: false,
        plan_features: planConfig.features,
        security_flags: {
          pre_registered: true,
          awaiting_payment: true,
          created_at: new Date().toISOString()
        }
      });
      console.log('✅ Perfil de usuario creado en estado pendiente');
    }

    // ===== FIN DE LA NUEVA LÓGICA =====

    // El resto del flujo es común para ambos casos: devolver el ID de usuario para el checkout.
    return NextResponse.json({
      success: true,
      userId: userId, // Devolvemos el ID del usuario (nuevo o existente)
      message: existingUser
        ? 'Usuario existente. Procediendo al pago para actualizar el plan.'
        : 'Usuario pre-registrado exitosamente. Procediendo al pago.',
      data: {
        email: email,
        planId: planId,
        awaitingPayment: true
      }
    });

  } catch (error) {
    console.error('Error procesando pre-registro:', error);
    return NextResponse.json({
      error: 'Error procesando la solicitud'
    }, { status: 500 });
  }
}
