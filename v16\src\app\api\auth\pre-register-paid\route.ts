// src/app/api/auth/pre-register-paid/route.ts
// API para pre-registrar usuarios de planes de pago antes del checkout

import { NextRequest, NextResponse } from 'next/server';
import { SupabaseAdminService } from '@/lib/supabase/admin';
import { getPlanConfiguration, getTokenLimitForPlan } from '@/config/plans';

interface PreRegisterRequest {
  email: string;
  password: string;
  customerName?: string;
  planId: string;
}

export async function POST(request: NextRequest) {
  console.log('🚀 [PRE-REGISTER-PAID] Endpoint llamado');

  try {
    console.log('📥 [PRE-REGISTER-PAID] Parseando body...');
    const body: PreRegisterRequest = await request.json();
    console.log('📥 [PRE-REGISTER-PAID] Body parseado:', { ...body, password: '[HIDDEN]' });

    const { email, password, customerName, planId } = body;

    // Validaciones básicas
    if (!email || !password || !planId) {
      console.log('❌ [PRE-REGISTER-PAID] Validación básica falló');
      return NextResponse.json({
        error: 'Email, contraseña y plan son requeridos'
      }, { status: 400 });
    }

    if (password.length < 6) {
      console.log('❌ [PRE-REGISTER-PAID] Contraseña muy corta');
      return NextResponse.json({
        error: 'La contraseña debe tener al menos 6 caracteres'
      }, { status: 400 });
    }

    console.log('🔍 [PRE-REGISTER-PAID] Validando plan...');
    // Validar que el plan existe y es de pago
    const planConfig = getPlanConfiguration(planId);
    if (!planConfig) {
      console.log('❌ [PRE-REGISTER-PAID] Plan no válido:', planId);
      return NextResponse.json({
        error: 'Plan no válido'
      }, { status: 400 });
    }

    if (planId === 'free') {
      console.log('❌ [PRE-REGISTER-PAID] Plan gratuito no permitido');
      return NextResponse.json({
        error: 'Este endpoint es solo para planes de pago'
      }, { status: 400 });
    }

    console.log('🔄 Pre-registrando usuario para plan de pago:', {
      email,
      planId,
      customerName,
      timestamp: new Date().toISOString()
    });

    try {
      // Crear usuario en Supabase sin confirmar email
      const userData = {
        name: customerName || email.split('@')[0],
        plan: planId,
        payment_verified: false,
        pre_registered: true,
        pre_registration_date: new Date().toISOString(),
        awaiting_payment: true
      };

      const { data, error } = await SupabaseAdminService.createUserWithPassword(
        email,
        password,
        userData,
        false // No enviar email de confirmación todavía
      );

      if (error) {
        console.error('Error creando usuario:', error);
        
        // Manejar error de email duplicado
        if (error.message?.includes('User already registered') || 
            error.message?.includes('email_address_not_authorized')) {
          return NextResponse.json({
            error: 'Ya existe una cuenta con este email. Por favor, usa otro email o inicia sesión.'
          }, { status: 409 });
        }

        return NextResponse.json({
          error: 'Error creando la cuenta. Por favor, inténtalo de nuevo.'
        }, { status: 500 });
      }

      if (!data?.user) {
        return NextResponse.json({
          error: 'Error creando la cuenta'
        }, { status: 500 });
      }

      const userId = data.user.id;
      console.log('✅ Usuario pre-registrado exitosamente:', userId);

      // Crear perfil de usuario en estado pendiente
      try {
        const profileResult = await SupabaseAdminService.createUserProfile({
          user_id: userId,
          subscription_plan: planId as 'usuario' | 'pro', // Aserción de tipo: las validaciones anteriores garantizan que planId es 'usuario' o 'pro'
          monthly_token_limit: getTokenLimitForPlan(planId),
          current_month_tokens: 0,
          current_month: new Date().toISOString().split('T')[0],
          payment_verified: false,
          plan_features: planConfig.features,
          security_flags: {
            pre_registered: true,
            awaiting_payment: true,
            created_at: new Date().toISOString()
          }
        });

        console.log('✅ Perfil de usuario creado en estado pendiente');

      } catch (profileError) {
        console.error('Error creando perfil:', profileError);
        // No fallar completamente, el perfil se puede crear después
      }

      return NextResponse.json({
        success: true,
        userId: userId,
        message: 'Usuario pre-registrado exitosamente',
        data: {
          email: email,
          planId: planId,
          awaitingPayment: true
        }
      });

    } catch (createError: any) {
      console.error('Error en pre-registro:', createError);
      
      return NextResponse.json({
        error: 'Error interno del servidor'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error procesando pre-registro:', error);
    return NextResponse.json({
      error: 'Error procesando la solicitud'
    }, { status: 500 });
  }
}
