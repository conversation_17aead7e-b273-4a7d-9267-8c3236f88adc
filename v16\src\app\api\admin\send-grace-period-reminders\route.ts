// src/app/api/admin/send-grace-period-reminders/route.ts
// Endpoint para enviar recordatorios de período de gracia que está por terminar

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { EmailNotificationService } from '@/lib/services/email/emailNotificationService';

// Lista de emails de administradores autorizados
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Agregar más emails de administradores aquí
];

// Clave secreta para cron jobs (opcional, para mayor seguridad)
const CRON_SECRET = process.env.CRON_SECRET;

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Iniciando envío de recordatorios de período de gracia');

    // Verificar autenticación por cron secret (si está configurado)
    const authHeader = request.headers.get('authorization');
    if (CRON_SECRET && authHeader !== `Bearer ${CRON_SECRET}`) {
      // Si no hay cron secret, verificar autenticación de usuario admin
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            getAll() {
              return request.cookies.getAll();
            },
            setAll() {},
          },
        }
      );

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
        console.log(`❌ Acceso denegado para usuario: ${user?.email || 'anónimo'}`);
        return NextResponse.json({
          error: 'Acceso denegado. Solo administradores pueden ejecutar esta acción.'
        }, { status: 403 });
      }

      console.log(`👤 Administrador autorizado: ${user.email}`);
    } else if (CRON_SECRET) {
      console.log('🤖 Ejecutado por cron job autorizado');
    }

    // Obtener parámetros de la request
    const body = await request.json().catch(() => ({}));
    const hoursBeforeExpiration = body.hoursBeforeExpiration || 24; // Por defecto, 24 horas antes

    // Calcular fecha límite para enviar recordatorios
    const reminderThreshold = new Date();
    reminderThreshold.setHours(reminderThreshold.getHours() + hoursBeforeExpiration);

    console.log(`📅 Buscando usuarios con período de gracia que expire en las próximas ${hoursBeforeExpiration} horas`);

    // Buscar usuarios en período de gracia que expiren pronto
    const { data: usersToRemind, error: searchError } = await supabaseAdmin
      .from('user_profiles')
      .select('user_id, subscription_plan, plan_expires_at, security_flags')
      .eq('auto_renew', false) // Suscripción cancelada
      .neq('subscription_plan', 'free') // No es plan gratuito
      .gt('plan_expires_at', new Date().toISOString()) // Aún no expirado
      .lt('plan_expires_at', reminderThreshold.toISOString()) // Expira pronto
      .limit(100); // Procesar máximo 100 por vez

    if (searchError) {
      throw new Error(`Error buscando usuarios para recordatorio: ${searchError.message}`);
    }

    if (!usersToRemind || usersToRemind.length === 0) {
      console.log('✅ No se encontraron usuarios que necesiten recordatorio');
      return NextResponse.json({
        success: true,
        message: 'No hay usuarios que necesiten recordatorio en este momento',
        result: {
          sent: 0,
          errors: [],
          hoursBeforeExpiration
        }
      });
    }

    // Filtrar solo usuarios realmente en período de gracia
    const usersInGrace = usersToRemind.filter(user => 
      user.security_flags?.subscription_cancelled === true
    );

    console.log(`📋 Encontrados ${usersInGrace.length} usuarios en período de gracia que necesitan recordatorio`);

    const errors: string[] = [];
    let sent = 0;

    // Enviar recordatorios
    for (const user of usersInGrace) {
      try {
        // Obtener información del usuario para el email
        const { data: userData } = await supabaseAdmin.auth.admin.getUserById(user.user_id);
        
        if (!userData.user?.email) {
          console.log(`⚠️ Usuario ${user.user_id} no tiene email, omitiendo`);
          continue;
        }

        const userName = userData.user.user_metadata?.name || userData.user.email.split('@')[0];
        const planName = user.subscription_plan === 'usuario' ? 'Usuario' : 'Pro';

        // Verificar si ya se envió un recordatorio recientemente (últimas 12 horas)
        const { data: recentNotification } = await supabaseAdmin
          .from('email_notifications')
          .select('sent_at')
          .eq('recipient_email', userData.user.email)
          .eq('type', 'grace_period_ending')
          .gte('sent_at', new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()) // Últimas 12 horas
          .single();

        if (recentNotification) {
          console.log(`⏭️ Recordatorio ya enviado recientemente a ${userData.user.email}, omitiendo`);
          continue;
        }

        // Enviar recordatorio
        const emailSent = await EmailNotificationService.sendGracePeriodEndingNotification(
          userData.user.email,
          userName,
          planName,
          user.plan_expires_at,
          user.user_id
        );

        if (emailSent) {
          sent++;
          console.log(`✅ Recordatorio enviado a: ${userData.user.email}`);
        } else {
          const errorMsg = `Error enviando recordatorio a ${userData.user.email}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }

      } catch (userError) {
        const errorMsg = `Error procesando usuario ${user.user_id}: ${userError instanceof Error ? userError.message : 'Unknown error'}`;
        console.error(errorMsg);
        errors.push(errorMsg);
      }
    }

    console.log(`🎯 Recordatorios completados: ${sent} enviados, ${errors.length} errores`);

    return NextResponse.json({
      success: true,
      message: 'Envío de recordatorios completado',
      result: {
        sent,
        errors,
        hoursBeforeExpiration,
        usersFound: usersInGrace.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error en envío de recordatorios:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Endpoint GET para obtener estadísticas de recordatorios
export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación de administrador
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
      return NextResponse.json({
        error: 'Acceso denegado'
      }, { status: 403 });
    }

    // Obtener estadísticas de recordatorios enviados
    const { data: recentReminders, error: remindersError } = await supabaseAdmin
      .from('email_notifications')
      .select('sent_at, recipient_email')
      .eq('type', 'grace_period_ending')
      .gte('sent_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Últimos 7 días
      .order('sent_at', { ascending: false });

    if (remindersError) {
      throw new Error('Error obteniendo estadísticas de recordatorios');
    }

    // Agrupar por día
    const remindersByDay = (recentReminders || []).reduce((acc, reminder) => {
      const day = reminder.sent_at.split('T')[0];
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      statistics: {
        totalRemindersLast7Days: recentReminders?.length || 0,
        remindersByDay,
        lastReminders: (recentReminders || []).slice(0, 10).map(r => ({
          email: r.recipient_email,
          sentAt: r.sent_at
        }))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error obteniendo estadísticas de recordatorios:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}
