# Guía de Desarrollo - OposiAI

Esta guía proporciona información detallada para desarrolladores que trabajan en el proyecto OposiAI.

## 🚀 Inicio Rápido

### Setup Inicial Completo

```bash
# 1. Clonar el repositorio
git clone <repository-url>
cd OposI/v16

# 2. Instalar dependencias y verificar setup
npm run setup

# 3. Configurar variables de entorno
cp .env.example .env.local
# Completar las variables necesarias

# 4. Verificar que todo funciona
npm run check-all

# 5. Iniciar desarrollo
npm run dev
```

## 📋 Scripts Disponibles

### Desarrollo
```bash
npm run dev              # Servidor de desarrollo
npm run build            # Build de producción
npm run start            # Servidor de producción
npm run fresh-start      # Limpiar cache e iniciar dev
```

### Calidad de Código
```bash
npm run lint             # Verificar linting
npm run lint:fix         # Corregir errores de linting automáticamente
npm run type-check       # Verificar tipos TypeScript
npm run check-all        # Ejecutar todas las verificaciones
```

### Testing
```bash
npm test                 # Ejecutar todos los tests
npm run test:watch       # Tests en modo watch
npm run test:coverage    # Tests con reporte de coverage
npm run test:integration # Solo tests de integración
```

### Utilidades
```bash
npm run clean            # Limpiar cache de Next.js
npm run db:types         # Generar tipos de Supabase
npm run setup            # Setup inicial del proyecto
```

## 🏗️ Flujo de Desarrollo

### 1. Crear Nueva Feature

```bash
# 1. Crear rama
git checkout -b feature/nueva-funcionalidad

# 2. Crear estructura de feature
mkdir -p src/features/nueva-funcionalidad/{components,hooks,services,types,__tests__}

# 3. Crear archivos base
touch src/features/nueva-funcionalidad/components/index.ts
touch src/features/nueva-funcionalidad/hooks/index.ts
touch src/features/nueva-funcionalidad/services/index.ts
touch src/features/nueva-funcionalidad/types/index.ts
```

### 2. Desarrollo Iterativo

```bash
# Durante el desarrollo, ejecutar frecuentemente:
npm run type-check       # Verificar tipos
npm run lint            # Verificar estilo
npm test -- --watch     # Tests en background
```

### 3. Antes de Commit

```bash
# Verificar que todo está correcto
npm run check-all

# Si hay errores de linting, corregir automáticamente
npm run lint:fix

# Hacer commit
git add .
git commit -m "feat: añadir nueva funcionalidad"
```

## 🔧 Configuración del Entorno

### Variables de Entorno Requeridas

```env
# Supabase (Requerido)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Stripe (Requerido para pagos)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...

# OpenAI (Requerido para IA)
OPENAI_API_KEY=sk-...

# App (Requerido)
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Variables Opcionales

```env
# Desarrollo
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true

# Analytics (Opcional)
NEXT_PUBLIC_GA_ID=G-...

# Logging (Opcional)
LOG_LEVEL=debug
```

## 🧪 Testing

### Estructura de Tests

```
src/
├── features/
│   └── auth/
│       └── __tests__/
│           ├── components/
│           │   └── LoginForm.test.tsx
│           ├── hooks/
│           │   └── useAuth.test.ts
│           └── services/
│               └── authService.test.ts
├── lib/
│   └── __tests__/
│       ├── services/
│       └── utils/
└── __tests__/
    ├── setup/
    │   ├── testConfig.ts
    │   └── testUtils.tsx
    └── integration/
        ├── authFlow.test.ts
        ├── paymentFlow.test.ts
        └── documentFlow.test.ts
```

### Escribir Tests

#### Test de Componente
```typescript
// src/features/auth/components/__tests__/LoginForm.test.tsx
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LoginForm } from '../LoginForm';

describe('LoginForm', () => {
  it('should submit form with valid data', async () => {
    const onSubmit = jest.fn();
    const user = userEvent.setup();
    
    render(<LoginForm onSubmit={onSubmit} />);
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /login/i }));
    
    expect(onSubmit).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    });
  });
});
```

#### Test de Hook
```typescript
// src/features/auth/hooks/__tests__/useAuth.test.ts
import { renderHook, act } from '@testing-library/react';
import { useAuth } from '../useAuth';

describe('useAuth', () => {
  it('should handle login', async () => {
    const { result } = renderHook(() => useAuth());
    
    await act(async () => {
      await result.current.signIn('<EMAIL>', 'password');
    });
    
    expect(result.current.user).toBeTruthy();
    expect(result.current.loading).toBe(false);
  });
});
```

#### Test de Servicio
```typescript
// src/features/auth/services/__tests__/authService.test.ts
import { authService } from '../authService';
import { createClient } from '@/lib/supabase/supabaseClient';

jest.mock('@/lib/supabase/supabaseClient');

describe('authService', () => {
  it('should sign in user', async () => {
    const mockSignIn = jest.fn().mockResolvedValue({
      data: { user: { id: '123', email: '<EMAIL>' } },
      error: null
    });
    
    (createClient as jest.Mock).mockReturnValue({
      auth: { signInWithPassword: mockSignIn }
    });
    
    const result = await authService.signIn('<EMAIL>', 'password');
    
    expect(result.user).toBeTruthy();
    expect(mockSignIn).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password'
    });
  });
});
```

### Coverage Goals

- **Servicios**: 80%+ coverage
- **Hooks**: 70%+ coverage  
- **Componentes**: 60%+ coverage
- **Utilidades**: 90%+ coverage

## 🔍 Debugging

### Herramientas de Debug

1. **React Developer Tools**
   - Instalar extensión del navegador
   - Inspeccionar componentes y estado

2. **Next.js Debug**
   ```bash
   # Habilitar debug de Next.js
   DEBUG=* npm run dev
   ```

3. **Supabase Debug**
   ```typescript
   // En desarrollo, habilitar logs de Supabase
   const supabase = createClient(url, key, {
     auth: {
       debug: process.env.NODE_ENV === 'development'
     }
   });
   ```

4. **VS Code Debugging**
   ```json
   // .vscode/launch.json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "Next.js: debug server-side",
         "type": "node",
         "request": "attach",
         "port": 9229,
         "skipFiles": ["<node_internals>/**"]
       }
     ]
   }
   ```

### Logs Estructurados

```typescript
// Usar console.log estructurado en desarrollo
const logger = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[INFO] ${message}`, data);
    }
  },
  error: (message: string, error?: any) => {
    console.error(`[ERROR] ${message}`, error);
  },
  debug: (message: string, data?: any) => {
    if (process.env.NEXT_PUBLIC_DEBUG === 'true') {
      console.debug(`[DEBUG] ${message}`, data);
    }
  }
};

// Uso
logger.info('User logged in', { userId: user.id });
logger.error('Failed to save document', error);
logger.debug('API response', response);
```

## 🚀 Performance

### Optimizaciones Recomendadas

1. **Lazy Loading de Componentes**
   ```typescript
   import dynamic from 'next/dynamic';
   
   const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
     loading: () => <div>Loading...</div>
   });
   ```

2. **Memoización**
   ```typescript
   import { memo, useMemo, useCallback } from 'react';
   
   const ExpensiveComponent = memo(({ data }) => {
     const processedData = useMemo(() => {
       return data.map(item => processItem(item));
     }, [data]);
     
     const handleClick = useCallback((id: string) => {
       // Handle click
     }, []);
     
     return <div>{/* Component JSX */}</div>;
   });
   ```

3. **Optimización de Imágenes**
   ```typescript
   import Image from 'next/image';
   
   <Image
     src="/image.jpg"
     alt="Description"
     width={500}
     height={300}
     priority={isAboveFold}
     placeholder="blur"
     blurDataURL="data:image/jpeg;base64,..."
   />
   ```

### Monitoreo de Performance

```typescript
// Medir performance de funciones
const measurePerformance = (name: string, fn: () => any) => {
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`${name} took ${end - start} milliseconds`);
  }
  
  return result;
};

// Uso
const result = measurePerformance('processDocument', () => {
  return processDocument(document);
});
```

## 🔧 Herramientas Recomendadas

### VS Code Extensions
- TypeScript and JavaScript Language Features
- Tailwind CSS IntelliSense
- ESLint
- Prettier
- Jest Runner
- GitLens
- Auto Rename Tag
- Bracket Pair Colorizer

### Chrome Extensions
- React Developer Tools
- Redux DevTools (si se usa)
- Lighthouse
- Web Vitals

### CLI Tools
```bash
# Instalar herramientas globales útiles
npm install -g @supabase/cli
npm install -g vercel
npm install -g typescript
```

## 📚 Recursos Adicionales

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Jest Documentation](https://jestjs.io/docs/getting-started)

---

¡Happy coding! 🚀
