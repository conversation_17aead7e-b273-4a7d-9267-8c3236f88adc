// src/lib/__tests__/permissionService.test.ts
// Tests para el servicio de permisos

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { PermissionService } from '@/lib/services/permissionService';
import { SupabaseAdminService } from '@/lib/supabase/admin';

// Mock de Supabase Admin Service
jest.mock('@/lib/supabase/admin');
const mockSupabaseAdmin = SupabaseAdminService as jest.Mocked<typeof SupabaseAdminService>;

describe('PermissionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkPermission', () => {
    it('should grant permission for valid user with sufficient plan', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 50000,
        current_month: '2025-01-01'
      });

      const permission = {
        feature: 'ai_tutor_chat',
        tokensRequired: 1000,
        minimumPlan: ['usuario', 'pro'],
        requiresPayment: true
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(true);
      expect(result.userPlan).toBe('usuario');
      expect(result.tokenInfo).toBeDefined();
    });

    it('should deny permission for insufficient plan', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 5000,
        current_month: '2025-01-01'
      });

      const permission = {
        feature: 'ai_tutor_chat',
        tokensRequired: 1000,
        minimumPlan: ['usuario', 'pro'],
        requiresPayment: true
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(false);
      expect(result.reason).toContain('requiere plan');
      expect(result.upgradeRequired).toBe(true);
      expect(result.suggestedPlan).toBe('usuario');
    });

    it('should deny permission for unverified payment', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: false,
        monthly_token_limit: 1000000,
        current_month_tokens: 50000,
        current_month: '2025-01-01'
      });

      const permission = {
        feature: 'ai_tutor_chat',
        tokensRequired: 1000,
        minimumPlan: ['usuario', 'pro'],
        requiresPayment: true
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(false);
      expect(result.reason).toContain('Pago no verificado');
      expect(result.upgradeRequired).toBe(false);
    });

    it('should deny permission for insufficient tokens', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 999500, // Only 500 tokens remaining
        current_month: '2025-01-01'
      });

      const permission = {
        feature: 'ai_tutor_chat',
        tokensRequired: 1000,
        minimumPlan: ['usuario', 'pro'],
        requiresPayment: true
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(false);
      expect(result.reason).toContain('Límite mensual de tokens alcanzado');
      expect(result.tokenInfo).toBeDefined();
      expect(result.upgradeRequired).toBe(true);
    });

    it('should handle non-existent user profile', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue(null);

      const permission = {
        feature: 'test_generation',
        tokensRequired: 100
      };

      const result = await PermissionService.checkPermission('non-existent', permission);

      expect(result.granted).toBe(false);
      expect(result.reason).toContain('Perfil de usuario no encontrado');
    });
  });

  describe('checkMultiplePermissions', () => {
    it('should check multiple permissions correctly', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 50000,
        current_month: '2025-01-01'
      });

      const permissions = [
        { feature: 'test_generation', tokensRequired: 500 },
        { feature: 'ai_tutor_chat', tokensRequired: 1000, minimumPlan: ['usuario', 'pro'] },
        { feature: 'study_planning', tokensRequired: 2000, minimumPlan: ['pro'] }
      ];

      const results = await PermissionService.checkMultiplePermissions('test-user', permissions);

      expect(results['test_generation'].granted).toBe(true);
      expect(results['ai_tutor_chat'].granted).toBe(true);
      expect(results['study_planning'].granted).toBe(false); // Requires pro plan
    });
  });

  describe('createFeaturePermission', () => {
    it('should create correct permission for test generation', () => {
      const permission = PermissionService.createFeaturePermission('test_generation', 500);

      expect(permission.feature).toBe('test_generation');
      expect(permission.tokensRequired).toBe(500);
      expect(permission.minimumPlan).toEqual(['free', 'usuario', 'pro']);
      expect(permission.requiresPayment).toBe(true);
    });

    it('should create correct permission for AI tutor chat', () => {
      const permission = PermissionService.createFeaturePermission('ai_tutor_chat', 1000);

      expect(permission.feature).toBe('ai_tutor_chat');
      expect(permission.tokensRequired).toBe(1000);
      expect(permission.minimumPlan).toEqual(['usuario', 'pro']);
      expect(permission.requiresPayment).toBe(true);
    });

    it('should create correct permission for study planning', () => {
      const permission = PermissionService.createFeaturePermission('study_planning', 2000);

      expect(permission.feature).toBe('study_planning');
      expect(permission.tokensRequired).toBe(2000);
      expect(permission.minimumPlan).toEqual(['pro']);
      expect(permission.requiresPayment).toBe(true);
    });

    it('should handle unknown features', () => {
      const permission = PermissionService.createFeaturePermission('unknown_feature', 100);

      expect(permission.feature).toBe('unknown_feature');
      expect(permission.tokensRequired).toBe(100);
      expect(permission.minimumPlan).toBeUndefined();
      expect(permission.requiresPayment).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      mockSupabaseAdmin.getUserProfile.mockRejectedValue(new Error('Database error'));

      const permission = {
        feature: 'test_generation',
        tokensRequired: 100
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(false);
      expect(result.reason).toContain('Error interno');
    });

    it('should handle network timeouts', async () => {
      mockSupabaseAdmin.getUserProfile.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );

      const permission = {
        feature: 'test_generation',
        tokensRequired: 100
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(false);
      expect(result.reason).toContain('Error interno');
    });
  });

  describe('edge cases', () => {
    it('should handle zero token requirements', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 49999,
        current_month: '2025-01-01'
      });

      const permission = {
        feature: 'test_generation',
        tokensRequired: 0
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(true);
    });

    it('should handle exact token limit', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 49000,
        current_month: '2025-01-01'
      });

      const permission = {
        feature: 'test_generation',
        tokensRequired: 1000 // Exactly at limit
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(true);
    });

    it('should handle month rollover correctly', async () => {
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);

      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 50000, // Was at limit last month
        current_month: lastMonth.toISOString().slice(0, 7) + '-01'
      });

      const permission = {
        feature: 'test_generation',
        tokensRequired: 1000
      };

      const result = await PermissionService.checkPermission('test-user', permission);

      expect(result.granted).toBe(true); // Should be allowed due to month reset
    });
  });
});
