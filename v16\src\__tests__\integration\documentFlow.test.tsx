// src/__tests__/integration/documentFlow.test.ts
// Tests de integración para flujos de procesamiento de documentos

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TEST_TIMEOUTS, MockFactory } from '../setup/testConfig';

// Mock de servicios
jest.mock('@/lib/services/planValidation');
jest.mock('@/lib/services/limitHandler');
jest.mock('@/lib/supabase/admin');

describe('Document Processing Flow Integration', () => {
  let user: any;
  let mockPlanValidation: any;
  let mockLimitHandler: any;
  let mockSupabaseAdmin: any;

  beforeEach(() => {
    jest.clearAllMocks();
    user = userEvent.setup();

    // Setup mocks
    mockPlanValidation = require('@/lib/services/planValidation').PlanValidationService;
    mockLimitHandler = require('@/lib/services/limitHandler').LimitHandler;
    mockSupabaseAdmin = require('@/lib/supabase/admin').SupabaseAdminService;
  });

  describe('Document Upload and Processing', () => {
    it('should handle complete document upload to test generation flow', async () => {
      // Mock user with sufficient permissions
      const mockUserProfile = MockFactory.createUserProfile({
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 10000
      });

      mockSupabaseAdmin.getUserProfile.mockResolvedValue(mockUserProfile);

      // Mock permission validation
      mockPlanValidation.validateFeatureAccess.mockResolvedValue({
        allowed: true,
        remainingUsage: {
          tokens: 990000,
          tests: 95,
          flashcards: 495
        }
      });

      // Mock limit checking
      mockLimitHandler.checkUserLimits.mockResolvedValue({
        canProceed: true,
        limits: [],
        warnings: []
      });

      // Mock document processing API
      global.fetch = jest.fn()
        .mockResolvedValueOnce(MockFactory.createApiResponse({
          success: true,
          documentId: 'doc-123',
          extractedText: 'Sample document content for testing...',
          wordCount: 150,
          estimatedTokens: 200
        }))
        .mockResolvedValueOnce(MockFactory.createApiResponse({
          success: true,
          testId: 'test-456',
          questions: [
            {
              id: 1,
              question: '¿Cuál es el concepto principal del documento?',
              options: ['A', 'B', 'C', 'D'],
              correctAnswer: 'A',
              explanation: 'Explicación detallada...'
            }
          ],
          tokensUsed: 5000
        }));

      const TestDocumentUploadComponent = () => {
        const [status, setStatus] = useState<string>('idle');
        const [result, setResult] = useState<any>(null);

        const handleFileUpload = async (file: File) => {
          try {
            setStatus('uploading');

            // Check permissions first
            const permission = await mockPlanValidation.validateFeatureAccess(
              'test-user',
              'test_generation',
              5000
            );

            if (!permission.allowed) {
              throw new Error('Permission denied');
            }

            // Upload document
            const formData = new FormData();
            formData.append('file', file);

            const uploadResponse = await fetch('/api/documents/upload', {
              method: 'POST',
              body: formData
            });

            const uploadResult = await uploadResponse.json();

            if (!uploadResult.success) {
              throw new Error('Upload failed');
            }

            setStatus('processing');

            // Generate test
            const testResponse = await fetch('/api/tests/generate', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                documentId: uploadResult.documentId,
                questionCount: 10,
                difficulty: 'medium'
              })
            });

            const testResult = await testResponse.json();

            if (!testResult.success) {
              throw new Error('Test generation failed');
            }

            setResult(testResult);
            setStatus('completed');

          } catch (error) {
            setStatus('error');
            setResult({ error: error.message });
          }
        };

        return (
          <div>
            <input
              type="file"
              accept=".pdf,.doc,.docx,.txt"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload(file);
              }}
              data-testid="file-input"
            />
            <div data-testid="status">{status}</div>
            {result && (
              <div data-testid="result">
                {result.error ? (
                  <div>Error: {result.error}</div>
                ) : (
                  <div>
                    Test generated with {result.questions?.length} questions
                    (Tokens used: {result.tokensUsed})
                  </div>
                )}
              </div>
            )}
          </div>
        );
      };

      render(<TestDocumentUploadComponent />);

      // Create mock file
      const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });

      // Upload file
      const fileInput = screen.getByTestId('file-input');
      await user.upload(fileInput, mockFile);

      // Wait for upload to complete
      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('uploading');
      });

      // Wait for processing to complete
      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('completed');
      }, { timeout: TEST_TIMEOUTS.INTEGRATION });

      // Verify result
      expect(screen.getByTestId('result')).toHaveTextContent(
        'Test generated with 1 questions (Tokens used: 5000)'
      );

      // Verify API calls
      expect(fetch).toHaveBeenCalledTimes(2);
      expect(mockPlanValidation.validateFeatureAccess).toHaveBeenCalledWith(
        'test-user',
        'test_generation',
        5000
      );
    });

    it('should handle document upload with insufficient permissions', async () => {
      // Mock user with insufficient permissions
      mockPlanValidation.validateFeatureAccess.mockResolvedValue({
        allowed: false,
        reason: 'Plan free no incluye generación de tests',
        upgradeRequired: true
      });

      const TestRestrictedUploadComponent = () => {
        const [error, setError] = useState<string>('');

        const handleUpload = async () => {
          const permission = await mockPlanValidation.validateFeatureAccess(
            'free-user',
            'test_generation',
            5000
          );

          if (!permission.allowed) {
            setError(permission.reason);
          }
        };

        return (
          <div>
            <button onClick={handleUpload}>Upload Document</button>
            {error && <div data-testid="error">{error}</div>}
          </div>
        );
      };

      render(<TestRestrictedUploadComponent />);

      await user.click(screen.getByRole('button', { name: 'Upload Document' }));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent(
          'Plan free no incluye generación de tests'
        );
      });
    });

    it('should handle token limit exceeded during processing', async () => {
      // Mock user near token limit
      mockPlanValidation.validateFeatureAccess.mockResolvedValue({
        allowed: false,
        reason: 'Límite mensual de tokens excedido',
        currentUsage: 49500,
        limit: 50000
      });

      const TestTokenLimitComponent = () => {
        const [message, setMessage] = useState<string>('');

        const handleProcessing = async () => {
          const permission = await mockPlanValidation.validateFeatureAccess(
            'limited-user',
            'test_generation',
            5000
          );

          if (!permission.allowed) {
            setMessage(permission.reason);
          }
        };

        return (
          <div>
            <button onClick={handleProcessing}>Process Document</button>
            {message && <div data-testid="message">{message}</div>}
          </div>
        );
      };

      render(<TestTokenLimitComponent />);

      await user.click(screen.getByRole('button', { name: 'Process Document' }));

      await waitFor(() => {
        expect(screen.getByTestId('message')).toHaveTextContent(
          'Límite mensual de tokens excedido'
        );
      });
    });
  });

  describe('Flashcard Generation Flow', () => {
    it('should generate flashcards from uploaded document', async () => {
      // Mock successful flashcard generation
      mockPlanValidation.validateFeatureAccess.mockResolvedValue({
        allowed: true,
        remainingUsage: { tokens: 995000, flashcards: 490 }
      });

      global.fetch = jest.fn()
        .mockResolvedValueOnce(MockFactory.createApiResponse({
          success: true,
          documentId: 'doc-789',
          extractedText: 'Document content for flashcards...'
        }))
        .mockResolvedValueOnce(MockFactory.createApiResponse({
          success: true,
          flashcards: [
            {
              id: 1,
              front: 'Pregunta de la tarjeta',
              back: 'Respuesta de la tarjeta',
              difficulty: 'medium'
            },
            {
              id: 2,
              front: 'Segunda pregunta',
              back: 'Segunda respuesta',
              difficulty: 'easy'
            }
          ],
          tokensUsed: 3000
        }));

      const TestFlashcardComponent = () => {
        const [flashcards, setFlashcards] = useState<any[]>([]);
        const [loading, setLoading] = useState(false);

        const generateFlashcards = async () => {
          setLoading(true);
          
          try {
            // Check permission
            const permission = await mockPlanValidation.validateFeatureAccess(
              'test-user',
              'flashcard_generation',
              3000
            );

            if (!permission.allowed) {
              throw new Error('Permission denied');
            }

            // Generate flashcards
            const response = await fetch('/api/flashcards/generate', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                documentId: 'doc-789',
                count: 10
              })
            });

            const result = await response.json();
            setFlashcards(result.flashcards);
          } finally {
            setLoading(false);
          }
        };

        return (
          <div>
            <button onClick={generateFlashcards} disabled={loading}>
              {loading ? 'Generating...' : 'Generate Flashcards'}
            </button>
            <div data-testid="flashcard-count">
              {flashcards.length} flashcards generated
            </div>
          </div>
        );
      };

      render(<TestFlashcardComponent />);

      await user.click(screen.getByRole('button', { name: 'Generate Flashcards' }));

      await waitFor(() => {
        expect(screen.getByTestId('flashcard-count')).toHaveTextContent(
          '2 flashcards generated'
        );
      }, { timeout: TEST_TIMEOUTS.INTEGRATION });

      expect(mockPlanValidation.validateFeatureAccess).toHaveBeenCalledWith(
        'test-user',
        'flashcard_generation',
        3000
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      // Mock API error
      global.fetch = jest.fn().mockResolvedValue(
        MockFactory.createApiResponse(
          { error: 'Internal server error' },
          { ok: false, status: 500 }
        )
      );

      const TestErrorHandlingComponent = () => {
        const [error, setError] = useState<string>('');

        const handleApiCall = async () => {
          try {
            const response = await fetch('/api/documents/upload');
            const result = await response.json();
            
            if (!response.ok) {
              throw new Error(result.error || 'API call failed');
            }
          } catch (err) {
            setError(err.message);
          }
        };

        return (
          <div>
            <button onClick={handleApiCall}>Make API Call</button>
            {error && <div data-testid="error">{error}</div>}
          </div>
        );
      };

      render(<TestErrorHandlingComponent />);

      await user.click(screen.getByRole('button', { name: 'Make API Call' }));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Internal server error');
      });
    });

    it('should handle network errors', async () => {
      // Mock network error
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      const TestNetworkErrorComponent = () => {
        const [error, setError] = useState<string>('');

        const handleNetworkCall = async () => {
          try {
            await fetch('/api/test-endpoint');
          } catch (err) {
            setError('Network connection failed');
          }
        };

        return (
          <div>
            <button onClick={handleNetworkCall}>Test Network</button>
            {error && <div data-testid="error">{error}</div>}
          </div>
        );
      };

      render(<TestNetworkErrorComponent />);

      await user.click(screen.getByRole('button', { name: 'Test Network' }));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Network connection failed');
      });
    });
  });
});
