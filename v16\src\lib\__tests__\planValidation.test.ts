// src/lib/__tests__/planValidation.test.ts
// Tests para el servicio de validación de planes

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { PlanValidationService } from '@/lib/services/planValidation';
import { SupabaseAdminService } from '@/lib/supabase/admin';

// Mock de Supabase Admin Service
jest.mock('@/lib/supabase/admin');
const mockSupabaseAdmin = SupabaseAdminService as jest.Mocked<typeof SupabaseAdminService>;

describe('PlanValidationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateFeatureAccess', () => {
    it('should allow access for free plan features', async () => {
      // Mock user profile
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 1000,
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.validateFeatureAccess(
        'test-user',
        'test_generation',
        500
      );

      expect(result.allowed).toBe(true);
      expect(result.remainingUsage).toBeDefined();
    });

    it('should deny access for premium features on free plan', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 1000,
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.validateFeatureAccess(
        'test-user',
        'ai_tutor_chat',
        500
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('no está disponible');
    });

    it('should deny access when payment not verified for paid plans', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: false,
        monthly_token_limit: 1000000,
        current_month_tokens: 1000,
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.validateFeatureAccess(
        'test-user',
        'ai_tutor_chat',
        500
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('pago no verificado');
    });

    it('should deny access when token limit exceeded', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 49800,
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.validateFeatureAccess(
        'test-user',
        'test_generation',
        500
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('límite mensual');
    });

    it('should reset tokens for new month', async () => {
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 49000,
        current_month: lastMonth.toISOString().slice(0, 7) + '-01'
      });

      mockSupabaseAdmin.upsertUserProfile.mockResolvedValue(true);

      const result = await PlanValidationService.validateFeatureAccess(
        'test-user',
        'test_generation',
        500
      );

      expect(result.allowed).toBe(true);
      expect(mockSupabaseAdmin.upsertUserProfile).toHaveBeenCalledWith(
        expect.objectContaining({
          current_month_tokens: 0
        })
      );
    });
  });

  describe('canUserPerformAction', () => {
    it('should allow action within plan limits', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 1000,
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.canUserPerformAction(
        'test-user',
        'generate_test',
        5
      );

      expect(result.allowed).toBe(true);
    });

    it('should deny action exceeding plan limits', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 1000,
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.canUserPerformAction(
        'test-user',
        'generate_test',
        15 // Exceeds free plan limit of 10
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('límite');
    });
  });

  describe('updateTokenUsage', () => {
    it('should update token usage successfully', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 1000,
        current_month: '2025-01-01'
      });

      mockSupabaseAdmin.upsertUserProfile.mockResolvedValue(true);

      const result = await PlanValidationService.updateTokenUsage(
        'test-user',
        500,
        'test_generation'
      );

      expect(result).toBe(true);
      expect(mockSupabaseAdmin.upsertUserProfile).toHaveBeenCalledWith(
        expect.objectContaining({
          current_month_tokens: 1500
        })
      );
    });

    it('should fail to update when exceeding limits', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 49800,
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.updateTokenUsage(
        'test-user',
        500,
        'test_generation'
      );

      expect(result).toBe(false);
      expect(mockSupabaseAdmin.upsertUserProfile).not.toHaveBeenCalled();
    });
  });

  describe('getUserAccessInfo', () => {
    it('should return complete access info for valid user', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 50000,
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.getUserAccessInfo('test-user');

      expect(result).toBeDefined();
      expect(result?.plan).toBe('usuario');
      expect(result?.paymentVerified).toBe(true);
      expect(result?.features).toContain('ai_tutor_chat');
      expect(result?.currentUsage.tokens).toBe(50000);
    });

    it('should return null for non-existent user', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue(null);

      const result = await PlanValidationService.getUserAccessInfo('non-existent');

      expect(result).toBeNull();
    });
  });

  describe('checkUpgradeNeeded', () => {
    it('should suggest upgrade when approaching token limit', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 45000, // 90% usage
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.checkUpgradeNeeded('test-user');

      expect(result).toBeDefined();
      expect(result?.recommended).toBe(true);
      expect(result?.reason).toContain('90%');
      expect(result?.suggestedPlan).toBe('usuario');
    });

    it('should not suggest upgrade for low usage', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 10000, // 20% usage
        current_month: '2025-01-01'
      });

      const result = await PlanValidationService.checkUpgradeNeeded('test-user');

      expect(result?.recommended).toBe(false);
    });
  });
});
