@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Clases personalizadas para transformaciones 3D de flashcards */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Animaciones para modal de pantalla completa */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-scaleIn {
    animation: scaleIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Estilos para el menú lateral colapsible */
  .sidebar-transition {
    transition: width 0.3s ease-in-out, padding 0.3s ease-in-out;
  }

  /* Animaciones para el calendario del plan de estudios */
  .calendario-fade-in {
    animation: calendarioFadeIn 0.3s ease-out;
  }

  .calendario-slide-in {
    animation: calendarioSlideIn 0.4s ease-out;
  }

  .calendario-day-hover {
    transition: all 0.2s ease-in-out;
  }

  .calendario-day-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .calendario-modal-overlay {
    animation: overlayFadeIn 0.3s ease-out;
  }

  .calendario-modal-content {
    animation: modalSlideUp 0.4s ease-out;
  }

  @keyframes calendarioFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes calendarioSlideIn {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes overlayFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes modalSlideUp {
    from {
      opacity: 0;
      transform: translateY(100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Transiciones suaves para cambios de estado */
  .calendario-estado-transition {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  /* Animación de pulso para días importantes */
  .calendario-pulso {
    animation: calendarioPulso 2s infinite;
  }

  @keyframes calendarioPulso {
    0% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
  }

  /* Tooltips mejorados para el menú colapsado */
  .tooltip-hover {
    position: relative;
  }

  .tooltip-hover:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 8px;
    padding: 6px 12px;
    background-color: #1f2937;
    color: white;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 1;
    pointer-events: none;
  }

  .tooltip-hover:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 2px;
    border: 4px solid transparent;
    border-right-color: #1f2937;
    z-index: 1000;
    pointer-events: none;
  }
}
