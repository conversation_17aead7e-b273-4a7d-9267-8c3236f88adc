'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { FiFileText, FiAlertCircle, FiArrowUp } from 'react-icons/fi';
import Link from 'next/link';

interface DocumentLimitStatusProps {
  className?: string;
}

interface LimitStatus {
  current: number;
  limit: number;
  remaining: number;
  isAtLimit: boolean;
  plan: string;
}

export default function DocumentLimitStatus({ className = "" }: DocumentLimitStatusProps) {
  const [status, setStatus] = useState<LimitStatus | null>(null);
  const [loading, setLoading] = useState(true);

  const loadLimitStatus = useCallback(async () => {
    try {
      setLoading(true);

      // Primero obtener el plan del usuario
      const planResponse = await fetch('/api/user/plan');

      if (planResponse.ok) {
        const planData = await planResponse.json();

        if (planData.plan === 'free') {
          // Para usuarios gratuitos, obtener estado detallado
          const freeResponse = await fetch('/api/auth/free-account-status');

          if (freeResponse.ok) {
            const freeData = await freeResponse.json();

            if (freeData.success && freeData.isFreeAccount && freeData.status) {
              // Verificar que las propiedades existan antes de acceder a ellas
              const usageCount = freeData.status.usageCount || {};
              const limits = freeData.status.limits || {};

              const currentDocs = typeof usageCount.documents === 'number' ? usageCount.documents : 0;
              const limitDocs = typeof limits.documents === 'number' ? limits.documents : 1;

              setStatus({
                current: currentDocs,
                limit: limitDocs,
                remaining: Math.max(0, limitDocs - currentDocs),
                isAtLimit: currentDocs >= limitDocs,
                plan: 'free'
              });
            } else {
              // Si no es cuenta gratuita o hay error, no mostrar límites
              setStatus(null);
            }
          } else {
            console.error('Error fetching free account status:', freeResponse.status);
            setStatus(null);
          }
        } else {
          // Usuario con plan de pago - sin límites
          setStatus({
            current: 0,
            limit: -1, // Ilimitado
            remaining: -1,
            isAtLimit: false,
            plan: planData.plan || 'paid'
          });
        }
      } else {
        // Error o usuario no autenticado
        setStatus(null);
      }
    } catch (error) {
      console.error('Error cargando estado de límites:', error);
      setStatus(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadLimitStatus();
  }, [loadLimitStatus]);

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 text-sm text-gray-500 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
        <span>Cargando límites...</span>
      </div>
    );
  }

  if (!status) {
    return null;
  }

  // Usuario con plan de pago - sin límites
  if (status.plan === 'paid' || status.limit === -1) {
    return (
      <div className={`flex items-center space-x-2 text-sm text-green-600 ${className}`}>
        <FiFileText className="w-4 h-4" />
        <span>Documentos ilimitados</span>
      </div>
    );
  }

  // Usuario con plan gratuito
  const percentage = (status.current / status.limit) * 100;
  
  return (
    <div className={`space-y-2 ${className}`}>
      {/* Barra de progreso */}
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center space-x-2">
          <FiFileText className="w-4 h-4 text-gray-500" />
          <span className="text-gray-700">
            Documentos: {status.current}/{status.limit}
          </span>
        </div>
        
        {status.isAtLimit && (
          <div className="flex items-center space-x-1 text-red-600">
            <FiAlertCircle className="w-4 h-4" />
            <span className="text-xs font-medium">Límite alcanzado</span>
          </div>
        )}
      </div>

      {/* Barra visual */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${
            status.isAtLimit 
              ? 'bg-red-500' 
              : percentage > 80 
                ? 'bg-yellow-500' 
                : 'bg-green-500'
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>

      {/* Mensaje de estado */}
      {status.isAtLimit ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <FiAlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm text-red-800 font-medium">
                Límite de documentos alcanzado
              </p>
              <p className="text-xs text-red-700 mt-1">
                Has alcanzado el límite de {status.limit} documento(s) para el plan gratuito.
              </p>
              <Link
                href="/upgrade-plan"
                className="inline-flex items-center mt-2 px-3 py-1 bg-red-600 text-white text-xs font-medium rounded hover:bg-red-700 transition-colors"
              >
                <FiArrowUp className="w-3 h-3 mr-1" />
                Actualizar Plan
              </Link>
            </div>
          </div>
        </div>
      ) : status.remaining === 0 ? (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <FiAlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm text-yellow-800 font-medium">
                Último documento disponible
              </p>
              <p className="text-xs text-yellow-700 mt-1">
                Este es tu último documento disponible en el plan gratuito.
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-xs text-gray-600">
          Te quedan {status.remaining} documento(s) disponible(s)
        </div>
      )}
    </div>
  );
}
